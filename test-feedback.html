<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback Component Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/framer-motion@5.0.0/dist/framer-motion.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-firestore.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: white;
            font-family: system-ui, -apple-system, sans-serif;
        }
        .console-output {
            background-color: #2a2a2a;
            color: #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        .button {
            background-color: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            margin-right: 8px;
        }
        .button:hover {
            background-color: #2563eb;
        }
        .button-danger {
            background-color: #ef4444;
        }
        .button-danger:hover {
            background-color: #dc2626;
        }
        .button-success {
            background-color: #10b981;
        }
        .button-success:hover {
            background-color: #059669;
        }
        .button-warning {
            background-color: #f59e0b;
        }
        .button-warning:hover {
            background-color: #d97706;
        }
        .modal {
            background-color: rgba(0, 0, 0, 0.7);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background-color: #2a2a2a;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }
        .modal-header {
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
            border-bottom: 1px solid #3a3a3a;
        }
        .modal-body {
            padding: 15px;
        }
        .modal-footer {
            padding: 15px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid #3a3a3a;
        }
        .popover {
            position: absolute;
            background-color: #2a2a2a;
            border-radius: 4px;
            padding: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }
        .slider {
            width: 100%;
            height: 8px;
            background-color: #4a4a4a;
            border-radius: 4px;
            appearance: none;
        }
        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #3b82f6;
            cursor: pointer;
        }
    </style>
</head>
<body class="p-6">
    <h1 class="text-3xl font-bold mb-4">Feedback Component Test</h1>
    
    <div class="mb-4">
        <button id="toggleIssuesBtn" class="button">Toggle "Has Issues" Flag</button>
        <button id="resetFeedbackBtn" class="button button-danger">Reset Feedback</button>
    </div>
    
    <h2 class="text-xl font-bold mb-3">Console Output:</h2>
    <div id="console" class="console-output"></div>
    
    <div id="feedback-container" class="bg-gray-800 p-4 rounded-lg">
        <!-- Feedback component will render here -->
    </div>

    <script type="text/babel">
        // Mock the types and interfaces needed for the feedback component
        const PHEFeedbackOpts = {
            SongDoesntMatchTitle: "Song doesn't match title",
            SongDoesntMatchVideo: "Song doesn't match video",
            SongIsntMusic: "Song isn't music",
            VideoIsntMusicVideo: "Video isn't a music video",
            ProblemWithSong: "Problem with song",
            ProblemWithVideo: "Problem with video",
            ProblemWithPlayback: "Problem with playback"
        };

        // Mock the Firebase submission function
        function submitFeedbackPowerHourEntry(feedbackData) {
            logToConsole("Submitting feedback: " + JSON.stringify(feedbackData, null, 2));
            return Promise.resolve();
        }

        function logToConsole(message) {
            const console = document.getElementById('console');
            console.innerHTML += `<div>${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }

        // Simple component to mock the required UI elements from NextUI
        const Button = ({ children, color = "default", variant = "solid", onClick, startContent, endContent, className }) => {
            const getColorClass = () => {
                switch(color) {
                    case "primary": return "button";
                    case "success": return "button button-success";
                    case "danger": return "button button-danger";
                    case "warning": return "button button-warning";
                    default: return "button";
                }
            };

            return (
                <button className={`${getColorClass()} ${className || ''}`} onClick={onClick}>
                    {startContent && <span className="mr-2">{startContent}</span>}
                    {children}
                    {endContent && <span className="ml-2">{endContent}</span>}
                </button>
            );
        };

        const Modal = ({ isOpen, onClose, children }) => {
            if (!isOpen) return null;
            
            return (
                <div className="modal" onClick={onClose}>
                    <div className="modal-content" onClick={e => e.stopPropagation()}>
                        {children}
                    </div>
                </div>
            );
        };

        const ModalHeader = ({ children }) => <div className="modal-header">{children}</div>;
        const ModalBody = ({ children }) => <div className="modal-body">{children}</div>;
        const ModalFooter = ({ children }) => <div className="modal-footer">{children}</div>;

        const Popover = ({ children, isOpen, onClose }) => {
            return children;
        };

        const PopoverTrigger = ({ children }) => children;

        const PopoverContent = ({ children, className }) => {
            return (
                <div className={`popover ${className || ''}`}>
                    {children}
                </div>
            );
        };

        const Tooltip = ({ content, children }) => {
            return children;
        };

        // Mock useDisclosure hook
        function useDisclosure() {
            const [isOpen, setIsOpen] = React.useState(false);
            return {
                isOpen,
                onOpen: () => setIsOpen(true),
                onClose: () => setIsOpen(false),
                onToggle: () => setIsOpen(!isOpen)
            };
        }

        // Simple motion div component to mock framer-motion
        const motion = {
            div: ({ children, initial, animate, exit, style }) => (
                <div style={style}>
                    {children}
                </div>
            )
        };

        const AnimatePresence = ({ children }) => children;

        // Mock Toast
        const toast = {
            success: (message) => logToConsole(`SUCCESS: ${message}`),
            error: (message) => logToConsole(`ERROR: ${message}`),
            info: (message) => logToConsole(`INFO: ${message}`)
        };
        
        // Icons mocks
        const FaThumbsUp = () => <span>👍</span>;
        const FaThumbsDown = () => <span>👎</span>;
        const FaBug = () => <span>🐛</span>;
        const FaForward = () => <span>⏩</span>;
        const FaRandom = () => <span>🔀</span>;
        const FaClock = () => <span>🕒</span>;
        const FaWrench = () => <span>🔧</span>;
        const FaMusic = () => <span>🎵</span>;

        // PHEntryFeedback component testing implementation
        const PHEntryFeedback = () => {
            const [feedbackVote, setFeedbackVote] = React.useState("");
            const [feedbackData, setFeedbackData] = React.useState({
                powerHourId: "test-power-hour-123",
                powerHourEntryId: "test-entry-456",
            });
            
            const [currentSongHasIssues, setCurrentSongHasIssues] = React.useState(false);
            const [currentStartTime, setCurrentStartTime] = React.useState(30);
            const [currentEndTime, setCurrentEndTime] = React.useState(90);

            // For time adjustment modal
            const { isOpen: isTimeModalOpen, onOpen: onTimeModalOpen, onClose: onTimeModalClose } = useDisclosure();
            const [newStartTime, setNewStartTime] = React.useState(currentStartTime);
            const [newEndTime, setNewEndTime] = React.useState(currentEndTime);

            // For issue resolution modal
            const { isOpen: isIssueModalOpen, onOpen: onIssueModalOpen, onClose: onIssueModalClose } = useDisclosure();
            const [selectedIssue, setSelectedIssue] = React.useState(null);

            const commonIssues = [
                PHEFeedbackOpts.SongDoesntMatchTitle,
                PHEFeedbackOpts.SongDoesntMatchVideo,
                PHEFeedbackOpts.SongIsntMusic,
                PHEFeedbackOpts.VideoIsntMusicVideo,
                PHEFeedbackOpts.ProblemWithSong,
                PHEFeedbackOpts.ProblemWithVideo,
                PHEFeedbackOpts.ProblemWithPlayback,
            ];

            React.useEffect(() => {
                window.toggleHasIssues = () => {
                    setCurrentSongHasIssues(!currentSongHasIssues);
                    logToConsole(`Song has issues: ${!currentSongHasIssues}`);
                };
                
                window.resetFeedback = () => {
                    setFeedbackData({
                        powerHourId: "test-power-hour-123",
                        powerHourEntryId: "test-entry-456",
                    });
                    setFeedbackVote("");
                    logToConsole("Feedback data reset");
                };
                
                document.getElementById('toggleIssuesBtn').addEventListener('click', window.toggleHasIssues);
                document.getElementById('resetFeedbackBtn').addEventListener('click', window.resetFeedback);
                
                return () => {
                    document.getElementById('toggleIssuesBtn').removeEventListener('click', window.toggleHasIssues);
                    document.getElementById('resetFeedbackBtn').removeEventListener('click', window.resetFeedback);
                };
            }, [currentSongHasIssues]);

            // Update slider values when the timeframe props change
            React.useEffect(() => {
                setNewStartTime(currentStartTime);
                setNewEndTime(currentEndTime);
            }, [currentStartTime, currentEndTime]);

            const toggleFeedbackVote = React.useCallback((upvote, downvote) => {
                if (upvote && feedbackVote === "up") {
                    setFeedbackVote("");
                } else if (downvote && feedbackVote === "down") {
                    setFeedbackVote("");
                } else {
                    setFeedbackVote(upvote ? "up" : downvote ? "down" : "");
                }
            }, [feedbackVote]);

            const updateFeedback = React.useCallback(({ upvote, downvote, type }) => {
                logToConsole(`Updating feedback: upvote=${upvote}, downvote=${downvote}, type=${type}`);
                
                if (upvote || downvote) {
                    toggleFeedbackVote(upvote, downvote);
                }

                let updatedFeedbackData = structuredClone(feedbackData);

                if (upvote) {
                    updatedFeedbackData.upvotes = 1;
                    delete updatedFeedbackData.downvotes;
                } else if (downvote) {
                    delete updatedFeedbackData.upvotes;
                    updatedFeedbackData.downvotes = 1;
                }

                if (type) {
                    updatedFeedbackData.feedback = {
                        ...updatedFeedbackData.feedback,
                        [type]: updatedFeedbackData.feedback?.[type] ? 0 : 1,
                    };
                }
                
                setFeedbackData(updatedFeedbackData);
                
                // Submit the feedback to the server
                submitFeedbackPowerHourEntry(updatedFeedbackData);
            }, [feedbackData, toggleFeedbackVote]);

            // Handle fixing the song timing
            const handleTimeAdjustment = React.useCallback(() => {
                logToConsole(`Adjusting timeframe: start=${newStartTime}, end=${newEndTime}`);
                setCurrentStartTime(newStartTime);
                setCurrentEndTime(newEndTime);
                toast.success("Timeframe adjusted! How does it sound now?");
                updateFeedback({ type: PHEFeedbackOpts.ProblemWithSong });
                onTimeModalClose();
            }, [newStartTime, newEndTime, onTimeModalClose, updateFeedback]);
            
            // Handle an issue with feedback and solution options
            const handleIssueSubmit = React.useCallback((issue) => {
                setSelectedIssue(issue);
                updateFeedback({ type: issue });
                onIssueModalOpen();
            }, [onIssueModalOpen, updateFeedback]);

            // Mock functions for the buttons
            const onSkipTrack = () => {
                logToConsole("Skipping to next track");
                onIssueModalClose();
            };
            
            const onAdjustTimeframe = (start, end) => {
                logToConsole(`Adjusting timeframe from component: start=${start}, end=${end}`);
                setCurrentStartTime(start);
                setCurrentEndTime(end);
            };
            
            const onFindAlternative = () => {
                logToConsole("Finding alternative version");
                onIssueModalClose();
            };

            return (
                <div>
                    {currentSongHasIssues && (
                        <motion.div 
                            style={{ 
                                margin: "0 0 0.75rem 0", 
                                padding: "0.5rem", 
                                borderRadius: "0.5rem", 
                                backgroundColor: "rgba(153, 27, 27, 0.7)", 
                                textAlign: "center", 
                                fontSize: "0.875rem", 
                                color: "white" 
                            }}
                        >
                            Having trouble with this song? Use the options below to fix it!
                        </motion.div>
                    )}
                    
                    <div className="flex flex-col items-center justify-center w-full pt-2 text-white">
                        {/* Quick fix options row */}
                        <div className="flex items-center justify-center gap-2 mb-3 flex-wrap">
                            <Tooltip content="Skip this song and move to the next one">
                                <Button 
                                    color="warning" 
                                    onClick={onSkipTrack} 
                                    className="min-w-0"
                                    startContent={<FaForward />}
                                >
                                    Skip
                                </Button>
                            </Tooltip>
                            
                            <Tooltip content="Adjust the start and end times of this song">
                                <Button 
                                    color="primary" 
                                    onClick={onTimeModalOpen} 
                                    className="min-w-0"
                                    startContent={<FaClock />}
                                >
                                    Timing
                                </Button>
                            </Tooltip>
                            
                            <Tooltip content="Find an alternative version of this song">
                                <Button 
                                    color="success" 
                                    onClick={onFindAlternative}
                                    className="min-w-0"
                                    startContent={<FaRandom />}
                                >
                                    Alternative
                                </Button>
                            </Tooltip>
                        </div>
                        
                        {/* Feedback buttons */}
                        <div className="flex items-center justify-center gap-3">
                            <Button
                                onClick={() => updateFeedback({ upvote: true })}
                                color={feedbackVote === "up" ? "success" : "default"}
                                endContent={<FaThumbsUp />}
                            >
                                Great Song
                            </Button>
                            
                            <Button
                                onClick={() => updateFeedback({ downvote: true })}
                                color={feedbackVote === "down" ? "danger" : "default"}
                                endContent={<FaThumbsDown />}
                            >
                                Bad Song
                            </Button>
                            
                            <div className="relative">
                                <Button 
                                    color="default" 
                                    endContent={<FaBug />}
                                    onClick={() => document.getElementById('issuePopover').classList.toggle('hidden')}
                                >
                                    Report Issue
                                </Button>
                                
                                <div id="issuePopover" className="popover hidden mt-2 bg-gray-700 p-3 rounded-lg border border-gray-600">
                                    <span className="text-lg font-medium mb-2 block text-white">What&apos;s the issue?</span>
                                    <div className="w-64">
                                        <ul className="space-y-2">
                                            {commonIssues.map((issue, index) => (
                                                <li
                                                    key={index}
                                                    className={`p-2 rounded-md cursor-pointer hover:bg-gray-600 flex items-center ${
                                                        feedbackData?.feedback && feedbackData.feedback[issue]
                                                            ? "bg-blue-900/50 text-blue-300"
                                                            : "text-white"
                                                    }`}
                                                    onClick={() => {
                                                        handleIssueSubmit(issue);
                                                        document.getElementById('issuePopover').classList.add('hidden');
                                                    }}
                                                >
                                                    <span className="mr-2"><FaWrench /></span>
                                                    {issue}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Time Adjustment Modal */}
                    <Modal isOpen={isTimeModalOpen} onClose={onTimeModalClose}>
                        <ModalHeader>Adjust Song Timeframe</ModalHeader>
                        <ModalBody>
                            <p className="text-sm mb-4">Find the right part of the song for your power hour:</p>
                            
                            <div className="mb-3">
                                <label className="text-sm text-gray-300 mb-1 block">Start Time: {newStartTime} seconds</label>
                                <input 
                                    type="range"
                                    min={0}
                                    max={Math.max(0, Math.min(newEndTime - 30, 180))}
                                    value={newStartTime}
                                    step={1}
                                    className="slider"
                                    onChange={(e) => setNewStartTime(Number(e.target.value))}
                                />
                            </div>
                            
                            <div className="mb-4">
                                <label className="text-sm text-gray-300 mb-1 block">End Time: {newEndTime} seconds</label>
                                <input 
                                    type="range"
                                    min={newStartTime + 30}
                                    max={300}
                                    value={newEndTime}
                                    step={1}
                                    className="slider"
                                    onChange={(e) => setNewEndTime(Number(e.target.value))}
                                />
                            </div>
                            
                            <p className="text-sm text-blue-300">
                                Clip Length: {newEndTime - newStartTime} seconds
                            </p>
                        </ModalBody>
                        <ModalFooter>
                            <Button color="danger" onClick={onTimeModalClose}>
                                Cancel
                            </Button>
                            <Button color="primary" onClick={handleTimeAdjustment}>
                                Apply Changes
                            </Button>
                        </ModalFooter>
                    </Modal>
                    
                    {/* Issue Resolution Modal */}
                    <Modal isOpen={isIssueModalOpen} onClose={onIssueModalClose}>
                        <ModalHeader>Fix This Song</ModalHeader>
                        <ModalBody>
                            <div className="p-3 bg-blue-900/30 rounded-lg mb-4">
                                <p className="text-sm mb-2">Issue reported: <span className="font-semibold">{selectedIssue}</span></p>
                                <p className="text-xs">Thanks for your feedback! Here&apos;s what you can do:</p>
                            </div>
                            
                            <div className="space-y-3">
                                <Button 
                                    color="warning" 
                                    className="w-full justify-start"
                                    onClick={onSkipTrack}
                                    startContent={<FaForward />}
                                >
                                    Skip to Next Song
                                </Button>
                                
                                <Button 
                                    color="primary" 
                                    className="w-full justify-start"
                                    onClick={() => {
                                        onIssueModalClose();
                                        onTimeModalOpen();
                                    }}
                                    startContent={<FaClock />}
                                >
                                    Adjust Song Timing
                                </Button>
                                
                                <Button 
                                    color="success" 
                                    className="w-full justify-start"
                                    onClick={onFindAlternative}
                                    startContent={<FaMusic />}
                                >
                                    Find Alternative Version
                                </Button>
                            </div>
                        </ModalBody>
                        <ModalFooter>
                            <Button color="default" variant="light" onClick={onIssueModalClose}>
                                Close
                            </Button>
                        </ModalFooter>
                    </Modal>
                </div>
            );
        };

        // Mount the component
        ReactDOM.render(
            <PHEntryFeedback />,
            document.getElementById('feedback-container')
        );
    </script>
</body>
</html>
