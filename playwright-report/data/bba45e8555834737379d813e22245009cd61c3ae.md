# Test info

- Name: Power Hour App Testing >> should navigate to homepage and verify core elements
- Location: /Users/<USER>/Code/power-hour-ai-next/tests/power-hour.spec.ts:17:7

# Error details

```
Error: page.goto: Test timeout of 30000ms exceeded.
Call log:
  - navigating to "http://localhost:3003/", waiting until "load"

    at /Users/<USER>/Code/power-hour-ai-next/tests/power-hour.spec.ts:19:16
```

# Test source

```ts
   1 | import { test, expect, Page } from '@playwright/test';
   2 |
   3 | // Helper function to log console messages for debugging
   4 | function setupConsoleListener(page: Page): void {
   5 |   page.on('console', (msg: any) => {
   6 |     const msgType = msg.type();
   7 |     console.log(`BROWSER ${msgType}: ${msg.text()}`);
   8 |   });
   9 | }
   10 |
   11 | test.describe('Power Hour App Testing', () => {
   12 |   test.beforeEach(async ({ page }: { page: Page }) => {
   13 |     // Setup console logging for each test
   14 |     setupConsoleListener(page);
   15 |   });
   16 |
   17 |   test('should navigate to homepage and verify core elements', async ({ page }: { page: Page }) => {
   18 |     // Visit the home page
>  19 |     await page.goto('/');
      |                ^ Error: page.goto: Test timeout of 30000ms exceeded.
   20 |     
   21 |     // Verify the app loaded correctly
   22 |     const title = await page.title();
   23 |     console.log(`Page title: ${title}`);
   24 |     
   25 |     // Take a screenshot for reference
   26 |     await page.screenshot({ path: 'homepage.png', fullPage: true });
   27 |     
   28 |     // Verify basic UI elements are present
   29 |     await expect(page.locator('body')).toBeVisible();
   30 |   });
   31 |   
   32 |   test('should create a new power hour', async ({ page }: { page: Page }) => {
   33 |     // Navigate to create power hour page
   34 |     await page.goto('/power-hour-ai/create-power-hour');
   35 |     await page.waitForLoadState('networkidle');
   36 |     
   37 |     console.log('Navigated to create power hour page');
   38 |     
   39 |     // Take a screenshot of the create page
   40 |     await page.screenshot({ path: 'create-power-hour.png', fullPage: true });
   41 |     
   42 |     // Check if the page loaded properly
   43 |     const createTitle = page.getByText(/Create a Power Hour/i);
   44 |     if (await createTitle.isVisible()) {
   45 |       console.log('Create power hour page loaded successfully');
   46 |       
   47 |       // Fill out the power hour creation form
   48 |       // Look for input fields and buttons based on their placeholders or text
   49 |       const titleInput = page.getByPlaceholder(/Enter a title/i);
   50 |       if (await titleInput.isVisible()) {
   51 |         await titleInput.fill('Test Power Hour ' + new Date().toISOString().slice(0, 10));
   52 |         console.log('Filled title field');
   53 |         
   54 |         // Look for theme input, genre selection, or other form inputs
   55 |         const themeInput = page.getByPlaceholder(/Theme/i);
   56 |         if (await themeInput.isVisible()) {
   57 |           await themeInput.fill('Testing with Playwright');
   58 |           console.log('Filled theme field');
   59 |         }
   60 |         
   61 |         // Find submit/create button
   62 |         const createButton = page.getByRole('button', { name: /create|generate|start/i });
   63 |         if (await createButton.isVisible()) {
   64 |           console.log('Found create button, will click to generate power hour');
   65 |           
   66 |           // Click the create button
   67 |           await createButton.click();
   68 |           
   69 |           // Wait for generation process to complete (this may take time)
   70 |           try {
   71 |             // Look for success indicator or result page
   72 |             await page.waitForURL(/.*power-hour-details.*|.*created.*|.*success.*/i, 
   73 |               { timeout: 60000 });
   74 |             console.log('Successfully created a new power hour');
   75 |             await page.screenshot({ path: 'power-hour-created.png', fullPage: true });
   76 |           } catch (error) {
   77 |             console.log('Timed out waiting for power hour creation to complete');
   78 |             await page.screenshot({ path: 'power-hour-creation-timeout.png', fullPage: true });
   79 |           }
   80 |         } else {
   81 |           console.log('Create button not found, UI may have changed');
   82 |         }
   83 |       } else {
   84 |         console.log('Could not find title input field');
   85 |       }
   86 |     } else {
   87 |       console.log('Create power hour UI elements not found, possible page load issue');
   88 |     }
   89 |   });
   90 |   
   91 |   test('should view and test any existing power hour', async ({ page }: { page: Page }) => {
   92 |     // Navigate to home page to find existing power hours
   93 |     await page.goto('/');
   94 |     await page.waitForLoadState('networkidle');
   95 |     
   96 |     // Look for links or cards to existing power hours
   97 |     const powerHourCards = page.locator('.power-hour-card, [data-testid="power-hour-card"]');
   98 |     const cardCount = await powerHourCards.count();
   99 |     
  100 |     console.log(`Found ${cardCount} power hour cards on the homepage`);
  101 |     
  102 |     if (cardCount > 0) {
  103 |       // Click on the first power hour
  104 |       await powerHourCards.first().click();
  105 |       
  106 |       // Wait for navigation to details page
  107 |       await page.waitForURL(/.*power-hour.*details.*/);
  108 |       console.log('Navigated to power hour details page');
  109 |       
  110 |       // Check if the start button is available
  111 |       const startButton = page.getByRole('button', { name: /start this power hour/i });
  112 |       if (await startButton.isVisible()) {
  113 |         console.log('Start button found, clicking to start power hour');
  114 |         await startButton.click();
  115 |         
  116 |         // Wait for the active power hour page to load
  117 |         await page.waitForURL(/.*active-power-hour.*/);
  118 |         console.log('Active power hour page loaded');
  119 |         
```