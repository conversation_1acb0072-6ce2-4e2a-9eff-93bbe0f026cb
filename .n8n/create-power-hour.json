{"nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-2080, 200], "id": "700848af-095a-490f-be37-1c95050efb28", "name": "When chat message received", "webhookId": "b754c77b-2bb3-4314-afa6-0c8454bd6914"}, {"parameters": {"operation": "create", "projectId": "power-hour-ai", "collection": "power-hours", "documentId": "={{ $json.UUID }}", "columns": "=search, createdAt, songCount, VideoCount, upVotes, downVotes, tags, songs, videos, entries, title"}, "type": "n8n-nodes-base.googleFirebaseCloudFirestore", "typeVersion": 1.1, "position": [-1660, 200], "id": "cc3eec80-e3a1-4a39-b9fe-8867d12221a1", "name": "Create Power Hour1", "credentials": {"googleFirebaseCloudFirestoreOAuth2Api": {"id": "YNk6ZH2AV4hrFjct", "name": "Firebase Project - Power Hour AI "}}}, {"parameters": {"action": "generate", "dataPropertyName": "UUID"}, "type": "n8n-nodes-base.crypto", "typeVersion": 1, "position": [-1840, 200], "id": "dc269956-60e6-4d89-9073-4ab0f3254813", "name": "Crypto1"}, {"parameters": {"assignments": {"assignments": [{"id": "439af847-ecca-4449-b813-30477b26ab6a", "name": "chatInput", "value": "={{ $('Webhook').item.json.search }}", "type": "string"}, {"id": "d7fd9a9c-04ca-455d-ba8b-3cbc2ef8195f", "name": "powerHourSchema", "value": "={{ $('Google Cloud Firestore').item.json.v2 }}", "type": "object"}, {"id": "0b495622-2b44-49a7-ae0a-aa7d3b21b341", "name": "powerHourSongsSchema", "value": "={{ $json.v1 }}", "type": "object"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1040, 200], "id": "d711d264-7eef-4a8c-b5a6-53748a1af19d", "name": "<PERSON>"}, {"parameters": {"projectId": "power-hour-ai", "collection": "schemas", "documentId": "=power-hour"}, "type": "n8n-nodes-base.googleFirebaseCloudFirestore", "typeVersion": 1.1, "position": [-1460, 200], "id": "c73172ab-1a1d-4250-a859-afce9880df90", "name": "Google Cloud Firestore", "credentials": {"googleFirebaseCloudFirestoreOAuth2Api": {"id": "YNk6ZH2AV4hrFjct", "name": "Firebase Project - Power Hour AI "}}}, {"parameters": {"projectId": "power-hour-ai", "collection": "schemas", "documentId": "songs"}, "type": "n8n-nodes-base.googleFirebaseCloudFirestore", "typeVersion": 1.1, "position": [-1240, 200], "id": "8332cd56-3f9c-4aec-b31f-2e31c0650315", "name": "Google Cloud Firestore1", "credentials": {"googleFirebaseCloudFirestoreOAuth2Api": {"id": "YNk6ZH2AV4hrFjct", "name": "Firebase Project - Power Hour AI "}}}, {"parameters": {"hasOutputParser": true, "messages": {"messageValues": [{"message": "Your objective is to generate a list of 60 songs for a power hour based on the user prompt."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-1840, 480], "id": "a97947f8-e429-47e5-a6c4-7e0aeb1372f9", "name": "Basic LLM Chain"}, {"parameters": {"jsonSchemaExample": "[{\n\t\"song_name\": \"test\",\n\t\"artist\": \"test\",\n    \"release_date\":\"test\"\n}]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-1660, 660], "id": "07526cdc-dbaa-4533-8cc5-7c3f68a92fc9", "name": "Structured Output Parser1"}, {"parameters": {"model": "o1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [-1840, 660], "id": "586003b4-39aa-41da-bfac-b2971bf29ca7", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "bNG5K0a3R8pOvzXW", "name": "OpenAi account"}}}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "entry", "renameField": true, "outputFieldName": "entries"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-1520, 960], "id": "eb261c86-3c0e-4605-bcee-3bb7fda1dbef", "name": "Aggregate"}, {"parameters": {"jsCode": "return {\n  fields: {\n    songs: {\n      arrayValue: {\n        values: $input.first().json.output.map((item) => ({\n          mapValue: {\n            fields: Object.entries(item).reduce((acc, [key, value]) => {\n              acc[key] = { stringValue: value };\n              return acc;\n            }, {}),\n          },\n        })),\n      },\n    },\n  },\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1100, 460], "id": "e9e2fa7d-5e83-4859-98cb-4f81b40d929a", "name": "Convert To Firestore Syntax"}, {"parameters": {"method": "PATCH", "url": "=https://firestore.googleapis.com/v1/projects/power-hour-ai/databases/(default)/documents/power-hours/{{ $('Create Power Hour1').item.json._id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleFirebaseCloudFirestoreOAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.toJsonString() }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-840, 460], "id": "72ca46f3-e015-48e9-bac6-9eb26ba8626d", "name": "Upsert Songs", "credentials": {"googleFirebaseCloudFirestoreOAuth2Api": {"id": "YNk6ZH2AV4hrFjct", "name": "Firebase Project - Power Hour AI "}}}, {"parameters": {"fieldToSplitOut": "fields.songs.arrayValue.values", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1460, 700], "id": "4d22766f-58f6-4f13-b43e-5dc72c7a34d3", "name": "For Each Song"}, {"parameters": {"url": "https://www.googleapis.com/customsearch/v1", "sendQuery": true, "specifyQuery": "json", "jsonQuery": "={\n      \"key\": \"AIzaSyACDFCPmZwfOqrM5R1bQJDE78L9tSJ1gP8\",\n      \"cx\": \"c76cb4b04b88b4b0d\",\n      \"q\": \"{{ $json.mapValue.fields.song_name.stringValue + ' by ' + $json.mapValue.fields.artist.stringValue }}\"\n    }", "options": {"timeout": 5000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-820, 700], "id": "1ce8d425-063e-453a-9777-2ad974c38172", "name": "Search for Video", "executeOnce": false, "notes": "https://programmablesearchengine.google.com/controlpanel/overview?cx=c76cb4b04b88b4b0d"}, {"parameters": {"jsCode": "// helper – converts any JS value into the Firestore “Value” format\nfunction toFirestoreValue(v) {\n  if (v === null) return { nullValue: null };\n\n  if (Array.isArray(v)) {\n    return {\n      arrayValue: { values: v.map(toFirestoreValue) },\n    };\n  }\n\n  switch (typeof v) {\n    case 'string':\n      return { stringValue: v };\n    case 'number':\n      // Firestore integers must be *strings*; doubles use doubleValue\n      return Number.isInteger(v)\n        ? { integerValue: v.toString() }\n        : { doubleValue: v };\n    case 'boolean':\n      return { booleanValue: v };\n    case 'object': {\n      return {\n        mapValue: {\n          fields: Object.fromEntries(\n            Object.entries(v).map(([k, val]) => [k, toFirestoreValue(val)])\n          ),\n        },\n      };\n    }\n    default:\n      throw new Error(`Unsupported value type: ${typeof v}`);\n  }\n}\n\n// assume the previous node (e.g. Set or HTTP) outputs\n// { entries: [ ...your array… ] }\nconst entries = $input.first().json.entries;\n\n// Build the Firestore-ready document body\nconst firestoreDoc = {\n  fields: {\n    entries: {\n      arrayValue: {\n        values: entries.map(toFirestoreValue), // each element is a mapValue\n      },\n    },\n  },\n};\n\n// n8n JS nodes must return an **array** of items\nreturn [\n  {\n    json: firestoreDoc,\n  },\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1160, 960], "id": "f703792e-5173-4f96-9717-830916c4e9a0", "name": "Convert To Firestore Syntax1"}, {"parameters": {"method": "PATCH", "url": "=https://firestore.googleapis.com/v1/projects/power-hour-ai/databases/(default)/documents/power-hours/{{ $('Create Power Hour1').item.json._id }}?updateMask.fieldPaths=entries", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleFirebaseCloudFirestoreOAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.toJsonString() }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-820, 960], "id": "e40110df-97aa-45c8-95da-c6b502c1f34d", "name": "Upsert Videos", "credentials": {"googleFirebaseCloudFirestoreOAuth2Api": {"id": "YNk6ZH2AV4hrFjct", "name": "Firebase Project - Power Hour AI "}}}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"entry.song\": {\n    \"name\": \"{{ $('For Each Song').item.json.mapValue.fields.song_name.stringValue.replace(/\"/g, '\\\\\"') }}\",\n    \"artist\": \"{{ $('For Each Song').item.json.mapValue.fields.artist.stringValue.replace(/\"/g, '\\\\\"') }}\",\n    \"release\": \"{{ $('For Each Song').item.json.mapValue.fields.release_date.stringValue.replace(/\"/g, '\\\\\"') }}\"\n  },\n  \"entry.video\": {\n    \"id\": \"{{ $json.items[0].link }}\",\n    \"title\": \"{{ $json.items[0].title.replace(/\"/g, '\\\\\"') }}\",\n    \"link\": \"{{ $json.items[0].link }}\",\n    \"thumbnail\": \"{{ $json.items[0].pagemap.cse_thumbnail[0].src }}\"\n  },\n  \"entry.backup_video\": {\n    \"id\": \"{{ $json.items[1].link }}\",\n    \"title\": \"{{ $json.items[1].title.replace(/\"/g, '\\\\\"') }}\",\n    \"link\": \"{{ $json.items[1].link }}\",\n    \"thumbnail\": \"{{ $json.items[1].pagemap.cse_thumbnail[0].src }}\"\n  }\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1840, 960], "id": "3601e19a-7d9b-4251-9aa6-b7818a75b366", "name": "Extract Entry Details"}, {"parameters": {"httpMethod": "POST", "path": "createPowerHour", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2080, 400], "id": "b250ddf9-c831-405f-a2ef-1f45526dfad9", "name": "Webhook", "webhookId": "2935b497-7bc6-45f8-a78a-a586281a577a"}, {"parameters": {"respondWith": "json", "responseBody": "={ \"powerHourId\": \"{{ $json._id }}\" }", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-1460, 0], "id": "89f9288e-219a-4a61-b648-b4dfbd366018", "name": "Respond to Webhook"}, {"parameters": {"content": "Check DB for existing Power Hour that fits search?\n"}, "type": "n8n-nodes-base.stickyNote", "position": [-2440, 460], "typeVersion": 1, "id": "e5e0f4d5-3c1a-46e4-b392-b1ec3894bdbd", "name": "Sticky Note1"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-2100, 20], "id": "f31bc79b-a272-4c88-a96e-99a79b45c941", "name": "When Executed by Another Workflow"}, {"parameters": {"content": "## Create DB entry\n", "height": 260, "width": 1000}, "type": "n8n-nodes-base.stickyNote", "position": [-1860, 140], "typeVersion": 1, "id": "25880b39-5f97-483e-9339-21a92035becf", "name": "Sticky Note2"}, {"parameters": {"content": "## Generate Song List", "height": 180, "width": 1180, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [-1860, 420], "typeVersion": 1, "id": "c6eea764-5bd4-4e74-9247-9689afe42a8f", "name": "Sticky Note3"}, {"parameters": {"content": "## Find a matching Video\n", "height": 220, "width": 820}, "type": "n8n-nodes-base.stickyNote", "position": [-1500, 640], "typeVersion": 1, "id": "ea924baa-589d-497f-8da1-51a545a500a4", "name": "Sticky Note4"}, {"parameters": {"content": "## Add Videos to Firestore\n", "height": 220, "width": 1200, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-1880, 880], "typeVersion": 1, "id": "d1704d15-f6cb-4763-a39b-234a4d420d53", "name": "Sticky Note5"}, {"parameters": {"content": "## Respond with Created PHID\n\n", "height": 240, "width": 340, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-1560, -100], "typeVersion": 1, "id": "b974b3d0-062b-4f95-8645-a904ff8adc7b", "name": "Sticky Note6"}], "connections": {"When chat message received": {"main": [[{"node": "Crypto1", "type": "main", "index": 0}]]}, "Create Power Hour1": {"main": [[{"node": "Google Cloud Firestore", "type": "main", "index": 0}, {"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Crypto1": {"main": [[{"node": "Create Power Hour1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Google Cloud Firestore": {"main": [[{"node": "Google Cloud Firestore1", "type": "main", "index": 0}]]}, "Google Cloud Firestore1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Convert To Firestore Syntax", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Convert To Firestore Syntax1", "type": "main", "index": 0}]]}, "Convert To Firestore Syntax": {"main": [[{"node": "Upsert Songs", "type": "main", "index": 0}]]}, "Upsert Songs": {"main": [[{"node": "For Each Song", "type": "main", "index": 0}]]}, "For Each Song": {"main": [[{"node": "Search for Video", "type": "main", "index": 0}]]}, "Search for Video": {"main": [[{"node": "Extract Entry Details", "type": "main", "index": 0}]]}, "Convert To Firestore Syntax1": {"main": [[{"node": "Upsert Videos", "type": "main", "index": 0}]]}, "Extract Entry Details": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Crypto1", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Crypto1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "e8692e78f81b00fc35accc9512daf5a2ef76b674d35104a70a3b10f2f1567fb7"}}