{"name": "power-hour-ai-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:dev": "next build", "build:prod": "next build && output:export", "start": "next start", "start:emulators": "cd functions && yarn run serve", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@firebase/app": "^0.12.0", "@firebase/database": "^1.0.15", "@firebase/firestore": "^4.7.12", "@nextui-org/accordion": "^2.2.7", "@nextui-org/autocomplete": "^2.3.9", "@nextui-org/avatar": "^2.2.6", "@nextui-org/badge": "^2.2.5", "@nextui-org/breadcrumbs": "^2.2.6", "@nextui-org/button": "^2.0.27", "@nextui-org/listbox": "^2.3.9", "@nextui-org/menu": "^2.2.9", "@nextui-org/popover": "^2.1.15", "@nextui-org/react": "^2.6.11", "@nextui-org/scroll-shadow": "^2.3.5", "@nextui-org/select": "^2.4.9", "@nextui-org/system": "^2.4.6", "@nextui-org/system-rsc": "^2.3.5", "@nextui-org/theme": "^2.4.5", "@react-aria/i18n": "^3.12.8", "@react-aria/overlays": "^3.27.0", "@react-aria/utils": "^3.28.2", "@react-aria/visually-hidden": "^3.8.22", "@uidotdev/usehooks": "^2.4.1", "clsx": "^2.1.1", "firebase": "^11.7.1", "framer-motion": "12.10.4", "idb": "7.1.1", "lodash": "^4.17.21", "next": "^15.3.2", "next-themes": "^0.2.1", "openai": "^4.26.0", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1", "react-icons": "^5.5.0", "react-router-dom": "^6.22.0", "react-toastify": "^10.0.4", "react-youtube": "^10.1.0"}, "devDependencies": {"@playwright/test": "^1.52.0", "@storybook/react": "^8.6.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/next": "^9.0.0", "@types/node": "22.15.17", "@types/react": "19.1.3", "@types/react-dom": "^19.1.3", "@types/react-toastify": "^4.1.0", "@types/react-youtube": "^7.10.0", "@types/testing-library__dom": "^7.5.0", "autoprefixer": "^10.4.18", "eslint": "^8", "eslint-config-next": "^15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.52.0", "postcss": "^8.4.35", "sass": "^1.69.7", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}, "packageManager": "yarn@4.7.0+sha512.5a0afa1d4c1d844b3447ee3319633797bcd6385d9a44be07993ae52ff4facabccafb4af5dcd1c2f9a94ac113e5e9ff56f6130431905884414229e284e37bb7c9"}