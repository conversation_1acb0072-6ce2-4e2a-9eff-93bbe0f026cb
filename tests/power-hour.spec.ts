import { test, expect, Page } from '@playwright/test';

// Helper function to log console messages for debugging
function setupConsoleListener(page: Page): void {
  page.on('console', (msg: any) => {
    const msgType = msg.type();
    console.log(`BROWSER ${msgType}: ${msg.text()}`);
  });
}

test.describe('Power Hour App Testing', () => {
  test.beforeEach(async ({ page }: { page: Page }) => {
    // Setup console logging for each test
    setupConsoleListener(page);
  });

  test('should navigate to homepage and verify core elements', async ({ page }: { page: Page }) => {
    // Visit the home page
    await page.goto('/');
    
    // Verify the app loaded correctly
    const title = await page.title();
    console.log(`Page title: ${title}`);
    
    // Take a screenshot for reference
    await page.screenshot({ path: 'homepage.png', fullPage: true });
    
    // Verify basic UI elements are present
    await expect(page.locator('body')).toBeVisible();
  });
  
  test('should create a new power hour', async ({ page }: { page: Page }) => {
    // Navigate to create power hour page
    await page.goto('/power-hour-ai/create-power-hour');
    await page.waitForLoadState('networkidle');
    
    console.log('Navigated to create power hour page');
    
    // Take a screenshot of the create page
    await page.screenshot({ path: 'create-power-hour.png', fullPage: true });
    
    // Check if the page loaded properly
    const createTitle = page.getByText(/Create a Power Hour/i);
    if (await createTitle.isVisible()) {
      console.log('Create power hour page loaded successfully');
      
      // Fill out the power hour creation form
      // Look for input fields and buttons based on their placeholders or text
      const titleInput = page.getByPlaceholder(/Enter a title/i);
      if (await titleInput.isVisible()) {
        await titleInput.fill('Test Power Hour ' + new Date().toISOString().slice(0, 10));
        console.log('Filled title field');
        
        // Look for theme input, genre selection, or other form inputs
        const themeInput = page.getByPlaceholder(/Theme/i);
        if (await themeInput.isVisible()) {
          await themeInput.fill('Testing with Playwright');
          console.log('Filled theme field');
        }
        
        // Find submit/create button
        const createButton = page.getByRole('button', { name: /create|generate|start/i });
        if (await createButton.isVisible()) {
          console.log('Found create button, will click to generate power hour');
          
          // Click the create button
          await createButton.click();
          
          // Wait for generation process to complete (this may take time)
          try {
            // Look for success indicator or result page
            await page.waitForURL(/.*power-hour-details.*|.*created.*|.*success.*/i, 
              { timeout: 60000 });
            console.log('Successfully created a new power hour');
            await page.screenshot({ path: 'power-hour-created.png', fullPage: true });
          } catch (error) {
            console.log('Timed out waiting for power hour creation to complete');
            await page.screenshot({ path: 'power-hour-creation-timeout.png', fullPage: true });
          }
        } else {
          console.log('Create button not found, UI may have changed');
        }
      } else {
        console.log('Could not find title input field');
      }
    } else {
      console.log('Create power hour UI elements not found, possible page load issue');
    }
  });
  
  test('should view and test any existing power hour', async ({ page }: { page: Page }) => {
    // Navigate to home page to find existing power hours
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Look for links or cards to existing power hours
    const powerHourCards = page.locator('.power-hour-card, [data-testid="power-hour-card"]');
    const cardCount = await powerHourCards.count();
    
    console.log(`Found ${cardCount} power hour cards on the homepage`);
    
    if (cardCount > 0) {
      // Click on the first power hour
      await powerHourCards.first().click();
      
      // Wait for navigation to details page
      await page.waitForURL(/.*power-hour.*details.*/);
      console.log('Navigated to power hour details page');
      
      // Check if the start button is available
      const startButton = page.getByRole('button', { name: /start this power hour/i });
      if (await startButton.isVisible()) {
        console.log('Start button found, clicking to start power hour');
        await startButton.click();
        
        // Wait for the active power hour page to load
        await page.waitForURL(/.*active-power-hour.*/);
        console.log('Active power hour page loaded');
        
        // Look for the Start Playback button and click it if found
        const playbackButton = page.getByRole('button', { name: /start playback/i });
        if (await playbackButton.isVisible()) {
          console.log('Playback button found, starting the power hour');
          await playbackButton.click();
          
          // Wait for the YouTube player to be visible
          try {
            await page.waitForSelector('iframe[src*="youtube"]', { timeout: 10000 });
            console.log('YouTube player loaded successfully');
            
            // Wait a moment to observe if the video is playing
            await page.waitForTimeout(5000);
            await page.screenshot({ path: 'active-power-hour.png', fullPage: true });
            
            // Test next song functionality if available
            const nextButton = page.getByRole('button', { name: /next|skip/i });
            if (await nextButton.isVisible()) {
              console.log('Testing next song functionality');
              await nextButton.click();
              await page.waitForTimeout(3000);
            }
          } catch (error) {
            console.log('YouTube iframe not found, possible playback issue');
            await page.screenshot({ path: 'playback-error.png', fullPage: true });
          }
        } else {
          console.log('Playback button not found, UI may have changed');
        }
      } else {
        console.log('Start button not found, cannot test active power hour');
      }
    } else {
      console.log('No existing power hours found. Create one first.');
    }
  });
});
