{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts src --no-eslintrc --config .eslintrc.json", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"axios": "^1.6.7", "cors": "^2.8.5", "firebase-admin": "^11.11.1", "firebase-functions": "^4.7.0", "firebase-tools": "^13.1.0", "openai": "^3.2.0", "reflect-metadata": "^0.2.1"}, "devDependencies": {"@types/axios": "^0.14.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}