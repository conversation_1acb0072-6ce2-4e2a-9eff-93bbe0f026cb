import * as functions from 'firebase-functions';

// Get Firebase config
let firebaseConfig;
try {
  firebaseConfig = functions.config();
} catch (e) {
  console.error("Error loading Firebase config:", e);
  firebaseConfig = {};
}

export const environment = {
  production: process.env.NODE_ENV === 'production',
  // First try Firebase config, then fallback to process.env
  openaiApiKey: firebaseConfig.openai?.key || process.env.OPENAI_API_KEY!,
  youtubeAPIKey: firebaseConfig.youtube?.key || process.env.YOUTUBE_API_KEY!,
}