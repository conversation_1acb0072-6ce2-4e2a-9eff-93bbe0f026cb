

import { logger } from "firebase-functions/v1";
import { PowerHour, PowerHourEntry, Song, Video } from "../[models]/models";
import { RTDB } from "../firebase";

const db = RTDB;

export const fetchPowerHour = async (powerHourId: string): Promise<PowerHour> => {
  const snapshot = await db.ref('power-hours/' + powerHourId).once('value');
  console.log("Snapshot: ", snapshot.val());
  if (snapshot.exists()) {
    return snapshot.val();
  } else {
    return {} as PowerHour;
  }
}

export const updatePowerHour = async (powerHour: Partial<PowerHour>): Promise<Partial<PowerHour>> => {
  if (!powerHour?.id) {
    logger.error("PowerHour ID is required. Couldnt updatePowerHour in RTDB from Cloud Functions");
    return powerHour;
  }
  const powerHourRef = db.ref('power-hours/' + powerHour.id);
  powerHourRef.update(powerHour);
  return powerHour;
}

export const updatePowerHourSongs = async (powerHourId: string, songs: Song[]): Promise<Song[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldnt updatePowerHourSongs in RTDB from Cloud Functions");
    return songs;
  }
  const powerHourRef = db.ref('power-hours/' + powerHourId + '/songs');
  powerHourRef.set(songs);
  return songs;
}

export const updatePowerHourVideos = async (powerHourId:string, videos:Video[]): Promise<Video[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldnt updatePowerHourVideos in RTDB from Cloud Functions");
    return videos;
  }
  const powerHourRef = db.ref('power-hours/' + powerHourId + '/videos');
  powerHourRef.set(videos);
  return videos;
}

export const updatePowerHourEntries = async (powerHourId:string, entries:PowerHourEntry[]): Promise<PowerHourEntry[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldnt updatePowerHourEntries in RTDB from Cloud Functions");
    return entries;
  }
  const powerHourRef = db.ref('power-hours/' + powerHourId + '/entries');
  powerHourRef.set(entries);
  return entries;
}