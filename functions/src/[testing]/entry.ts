/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

// Start writing functions
// https://firebase.google.com/docs/functions/typescript

import * as logger from "firebase-functions/logger";
import { songsToVideosGoogleSearch } from "../[services]/songs-to-videos-google-search/songs-to-videos-google-search";
// import * as cors from 'cors';
import * as functions from 'firebase-functions';

const cors = require('cors')({
  origin: ['http://localhost:3000', 'https://power-hour-ai.web.app'],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// Fix for Gen 1 functions compatibility
const TestingEntry = functions.https.onRequest(async (request: any, response: any) => {
  // Handle preflight OPTIONS request directly
  if (request.method === 'OPTIONS') {
    response.set('Access-Control-Allow-Origin', request.headers.origin || '*');
    response.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    response.set('Access-Control-Max-Age', '3600');
    response.status(204).send('');
    return;
  }

  cors(request, response, async () => {

    console.log(request.body)
    const payload = request.body;
    const operation: string = payload.operation;
    const content = payload.content;

    let result;

    try {

      switch (operation) {

        case "song-to-video-google-search":
          result = await songsToVideosGoogleSearch(content)
          break;


      }

      response.set('Access-Control-Allow-Origin', request.headers.origin || '*');
      response.status(200).send(result);

    } catch (error) {
      logger.error("Error in Power Hour Pipeline", error);
      // Set CORS headers for the error response
      response.set('Access-Control-Allow-Origin', request.headers.origin || '*');
      response.status(500).send(error);
    }
  })
});

export default TestingEntry;