import { Configuration, OpenA<PERSON>pi, ChatCompletionRequestMessage } from "openai";
import { generateSongListPrompt } from "../[prompts]/generate-song-list";
import { PowerHour, Song } from "../[models]/models";
import { logger } from "firebase-functions/v1";
import { updatePowerHourSongs } from "../[RTDB]/rtdb";
import { songsToJSONPrompt } from "../[prompts]/songs-to-json";
import { environment } from "../environment";

const openAIKey: string = environment.openaiApiKey;

const generateSongList = async (
  search: string,
  count: number,
  disallow?: Song[],
  pH?: PowerHour
): Promise<{ songs: Song[]; pH?: PowerHour }> => {
  if (!search) {
    throw new Error("No search parameter provided");
  }

  if (!count) {
    throw new Error("No count parameter provided");
  }

  logger.info(`Starting song list generation for search: ${search}, count: ${count}`);
  logger.info(`Using OpenAI Key: ${openAIKey ? "Key is set" : "Key is NOT set"}`);

  const configuration = new Configuration({
    apiKey: openAIKey,
  });
  const openai = new OpenAIApi(configuration);

  let songListGenerationPrompt: { messages: ChatCompletionRequestMessage[] };
  let songListToJSONPrompt: { messages: ChatCompletionRequestMessage[] };
  let generatedSongsContent: string | null;
  let songsAsJSONContent: { songs: Song[] };

  /**
   * Prompt for generating song list
   */
  try {
    songListGenerationPrompt = await generateSongListPrompt(search, disallow || [], count);
    logger.info("Generated song list prompt successfully");
  } catch (e) {
    logger.error(`Error generating song list prompt: ${e}`);
    throw new Error("Error generating song list prompt: " + e);
  }

  /**
   * Generate song list
   */
  try {
    generatedSongsContent = await generateSongs(openai, songListGenerationPrompt.messages);
    logger.info(`Generated songs content: ${generatedSongsContent ? "Content generated" : "No content"}`);
    if (!generatedSongsContent) {
      throw new Error("Unable to generate song list");
    }
  } catch (e) {
    logger.error(`Error generating song list: ${e}`);
    throw new Error("Error generating song list: " + e);
  }

  /**
   * Prompt for converting song list to JSON
   */
  try {
    songListToJSONPrompt = await songsToJSONPrompt(generatedSongsContent);
  } catch (e) {
    logger.error("Error generating song list to JSON prompt: ", e);
    throw new Error("Error generating song list to JSON prompt: " + e);
  }

  /**
   * Convert song list to JSON
   */
  try {
    songsAsJSONContent = await songsAsJSON(openai, generatedSongsContent, songListToJSONPrompt.messages);
    console.log("songsAsJSONContent: ", songsAsJSONContent);
  } catch (e) {
    logger.error("Error converting song list to JSON: ", e);
    throw new Error("Error converting song list to JSON: " + e);
  }

  /**
   * Return song list
   */
  try {
    if (songsAsJSONContent?.songs?.length > count) {
      logger.error(`An additional ${songsAsJSONContent?.songs?.length - count} songs were produced. Returning first ${count}.`);
      songsAsJSONContent.songs = songsAsJSONContent.songs.slice(0, count);
    }
    if (songsAsJSONContent) {
      logger.info("generate Song List complete");
      logger.info("generated: " + songsAsJSONContent?.songs?.length + " Songs");

      if (pH) {
        if (pH?.id) {
          updatePowerHourSongs(pH?.id, songsAsJSONContent.songs);
        }
        pH.songs = songsAsJSONContent.songs;
        return { songs: songsAsJSONContent.songs, pH: { ...pH, songs: songsAsJSONContent.songs } };
      }
      return { songs: songsAsJSONContent.songs };
    } else {
      throw new Error("Unable to generate song list. No Power Hour provided.");
    }
  } catch (e) {
    logger.error("Error generating song list: ", e);
    throw new Error("Error generating song list: " + e);
  }
};

const generateSongs = async (
  openai: OpenAIApi,
  messages: ChatCompletionRequestMessage[]
): Promise<string | null> => {
  try {
    // Use chat completion instead of createCompletion for GPT-4
    const generatedSongsCompletion = await openai.createChatCompletion({
      model: "gpt-4",
      messages: messages as any[]
    });

    return generatedSongsCompletion.data.choices[0]?.message?.content || null;
  } catch (error) {
    logger.error(`Error in generateSongs: ${error}`);
    throw error;
  }
};

const songsAsJSON = async (
  openai: OpenAIApi,
  songsAsList: string,
  messages: ChatCompletionRequestMessage[]
): Promise<{ songs: Song[] }> => {
  try {
    // Use chat completion instead of createCompletion for GPT-4
    const songsJSON = await openai.createChatCompletion({
      model: "gpt-4",
      messages: messages as any[]
    });

    const content = songsJSON.data.choices[0]?.message?.content;
    logger.info(`Songs as JSON content: ${content ? "Content available" : "No content"}`);

    if (!content) {
      throw new Error("Unable to convert songs to JSON.");
    }

    // Log the raw content for debugging
    logger.info(`Raw content from OpenAI: ${content.substring(0, 100)}...`);

    // Try to extract JSON from the content if it's not pure JSON
    let jsonContent = content;

    // If the content starts with text (not a JSON structure)
    if (content.trim().startsWith('Here') || !content.trim().startsWith('{')) {
      logger.info("Content appears to be plain text, attempting to extract JSON");

      // Look for JSON-like structures
      const jsonMatch = content.match(/(\{[\s\S]*\})/);
      if (jsonMatch && jsonMatch[0]) {
        jsonContent = jsonMatch[0];
        logger.info(`Extracted potential JSON: ${jsonContent.substring(0, 100)}...`);
      } else {
        // No JSON structure found, try to convert it manually
        logger.info("No JSON structure found, creating JSON manually");

        // Split the text by lines and look for song entries
        const lines = content.split('\n').filter(line => line.trim());
        const songs: Song[] = [];

        let currentSong: Partial<Song> = {};

        for (const line of lines) {
          if (line.includes('Title:')) {
            if (currentSong.title) {
              // Save previous song and start a new one
              songs.push(currentSong as Song);
              currentSong = {};
            }
            currentSong.title = line.split('Title:')[1]?.trim();
          } else if (line.includes('Artist:')) {
            currentSong.artist = line.split('Artist:')[1]?.trim();
          } else if (line.includes('Year:')) {
            const year = line.split('Year:')[1]?.trim();
            currentSong.year = parseInt(year) || 0;
          }
        }

        // Add the last song if it exists
        if (currentSong.title && currentSong.artist) {
          songs.push(currentSong as Song);
        }

        // Create a valid JSON structure
        if (songs.length > 0) {
          // Add required fields to songs
          const processedSongs = songs.map((song, index) => ({
            ...song,
            id: `song-${index + 1}`,
            idealStartTime: 0,
            idealEndTime: 60
          }));

          return { songs: processedSongs };
        } else {
          throw new Error("Couldn't extract songs from the text response");
        }
      }
    }

    try {
      // Try to parse the JSON content
      return JSON.parse(jsonContent);
    } catch (parseError) {
      logger.error(`JSON parse error: ${parseError}`);
      throw new Error(`Failed to parse JSON: ${parseError}`);
    }
  } catch (error) {
    logger.error(`Error in songsAsJSON: ${error}`);
    throw error;
  }
};

export default generateSongList;
