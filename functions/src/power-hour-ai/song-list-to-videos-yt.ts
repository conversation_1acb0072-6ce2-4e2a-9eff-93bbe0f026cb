import { logger } from 'firebase-functions/v1';
import { PowerHour, Song, Video } from '../[models]/models';
import { YouTubeSearchResponse, searchForMusicVideo } from '../[services]/search-for-music-video';
import { updatePowerHourVideos } from '../[RTDB]/rtdb';
import * as CacheService from '../[services]/videoId-cache-service';

/**
 * Validate videos for a list of songs
 * If we have a video in cache, use it instead of calling the youtube API
 * 
 * @param songs list of songs to find videos for
 * @param pH existing power hour being generated
 * @returns songs, videos and power hour
 */
export async function searchForVideosYT(songs: Partial<Song>[], pH?: PowerHour): Promise<{ songs: Partial<Song>[], videos: Video[], pH?: Partial<PowerHour> }> {
  const videos: Video[] = [];
  for (const song of songs) {
    if (song.id === undefined || song.title === undefined || song.artist === undefined) {
      logger.error(`Song Id, title or artist is undefined for song: ${song}`);
      continue
    };
    const query = `${song.title} ${song.artist}`;

    const cachedVideo = await CacheService.get(song.title, song.artist);

    if (cachedVideo) {
      logger.log(`Cache hit "${query}": ${cachedVideo.id}`);

      videos.push({
        id: cachedVideo.id,
        songId: song.id,
        artist: song.artist.toLowerCase(),
        title: cachedVideo.title,
        description: cachedVideo.description,
        url: `https://www.youtube.com/watch?v=${cachedVideo.id}`,
        thumbnail: cachedVideo.thumbnail,
        provider: 'YouTube',
        videoStatus: { videoId: cachedVideo.id, status: 'Available', timeVerified: Date.now() }
      });

    } else {
      // logger.log(`Cache Miss ${query}`);
      try {
        const results: YouTubeSearchResponse = await searchForMusicVideo(song.title, song.artist);


        if (results?.items?.length > 0) {
          const topResult = results.items[0];
          const videoId = results.items[0].id.videoId;
          const video = {
            id: videoId,
            songId: song.id,
            artist: song.artist.toLowerCase(),
            title: topResult.snippet.title,
            description: topResult.snippet.description,
            url: `https://www.youtube.com/watch?v=${videoId}`,
            thumbnail: topResult.snippet.thumbnails.default.url,
            provider: 'YouTube',
            videoStatus: { videoId, status: 'Available', timeVerified: Date.now() }
          }
          videos.push(video);
          logger.log(`Found video for "${query}": https://www.youtube.com/watch?v=${videoId}`);
          CacheService.set(song.title, song.artist, video);
        } else {
          logger.error(`No video found for "${query}".`);
        }
      } catch (error) {
        logger.error(`Error from YouTube API:`, error);
      }
    }
  }
  logger.log("search for music videos complete");

  if (songs.length != videos.length) {
    logger.error(`Could not complete video for songs falling back to AI`);
  }
  if (pH?.id) {
    logger.info(`Updating Power Hour with videos`);
    updatePowerHourVideos(pH?.id, videos);
  }
  return { songs, videos, pH: { ...pH, videos } };
}

