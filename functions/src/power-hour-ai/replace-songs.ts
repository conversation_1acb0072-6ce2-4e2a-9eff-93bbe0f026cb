import { Configuration, OpenAIApi, ChatCompletionRequestMessage } from "openai";
import { PowerHour, Song } from "../[models]/models";
import { replaceSongsPrompt } from "../[prompts]/replace-songs";
import * as logger from "firebase-functions/logger";
import { environment } from "../environment";


const openAIKey = environment.openaiApiKey;


const replaceSongs = async (songsToReplace: Song[], pH?: PowerHour) => {
  const search = pH?.search || pH?.title;
  if (!search || !songsToReplace) {
    throw new Error("Either search parameter or songs to replace not provided.");
  }

  logger.info(`REPLACING ${songsToReplace.length} songs`);

  const prompt = replaceSongsPrompt(search, songsToReplace);

  const configuration = new Configuration({
    apiKey: openAIKey,
  });
  const openai = new OpenAIApi(configuration);

  const messages: ChatCompletionRequestMessage[] = prompt.messages;

  const completion = await openai.createChatCompletion({
    messages,
    model: "gpt-4o",
  });

  console.log("Completion: ", completion)
  const content = completion.data.choices[0]?.message?.content;
  if (content) {
    const parsedSongs = <Song[]>JSON.parse(content);
    return { songs: parsedSongs, pH: { ...pH, songs: parsedSongs } };
  } else {
    throw new Error("Unable to generate song list. Please try again.");
  }
};

export default replaceSongs;