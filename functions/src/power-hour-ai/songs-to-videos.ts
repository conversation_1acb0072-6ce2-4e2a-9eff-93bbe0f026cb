import { logger } from 'firebase-functions/v1';
import { PowerHour, Song, Video } from '../[models]/models';
import { updatePowerHourVideos } from '../[RTDB]/rtdb';
import { songListToVideos } from '../[services]/songs-to-videos-openai/song-list-to-videos-ai';
import { songsToVideosGoogleSearch } from '../[services]/songs-to-videos-google-search/songs-to-videos-google-search';

/**
 * Validate videos for a list of songs
 * If we have a video in cache, use it instead of calling the youtube API
 * 
 * @param songs list of songs to find videos for
 * @param pH existing power hour being generated
 * @returns songs, videos and power hour
 */
export async function songsToVideos(songs: Partial<Song>[], pH?: PowerHour): Promise<{ songs: Partial<Song>[], videos: Video[], pH?: Partial<PowerHour> }> {
  if (!songs) {
    logger.error("No Songs in songsToVideos()");
  }
  let videos: Video[] = [];
  try {

    // try google search
    videos = await songsToVideosGoogleSearch(songs)

    // fallback youtube search
    // might not need

    // fallback to AI
    const fromAI = await songListToVideos(songs, pH);
    videos = fromAI.videos.map((video) => ({ ...video, videoStatus: { videoId: video.id, status: 'Available', timeVerified: Date.now() } }));


    if (songs.length != videos.length) {
      logger.error(`Video Length doesnt not match Song List length`);

    }
    if (pH?.id) {
      logger.info(`Updating Power Hour with videos`);
      updatePowerHourVideos(pH?.id, videos);
    }
  } catch (error) {
    logger.error("failed to get videos", error)
  }
  return { songs, videos, pH: { ...pH, videos } };
}

