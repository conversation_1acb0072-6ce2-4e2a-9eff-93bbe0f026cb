import { logger } from "firebase-functions/v1";
import { PowerHour, PowerHourEntry, Song, Video } from "../[models]/models";
import { updatePowerHourEntries } from "../[RTDB]/rtdb";


export const buildPHEntries = async (songs?: Song[], videos?: Video[], pH?: PowerHour): Promise<{ entries: PowerHourEntry[], pH?: PowerHour }> => {
  const songList = songs || pH?.songs;
  const videoList = videos || pH?.videos;

  if (!songList || !videoList) {
    logger.error("No songs or videos provided during [buildPHEntries]")
    throw new Error("No songs or videos provided during [buildPHEntries]");
  }

  if (songList.length !== videoList.length) {
    logger.warn("Song list and video list are not the same length, using video list length");
  }

  const entries: PowerHourEntry[] = videoList.map((video, index) => {
    const matchingSong: Song = <Song>songList?.find(
      (song: Song) => song.id === video.songId
    );

    return {
      id: video.id + "_" + matchingSong.id,
      songId: matchingSong.id,
      videoId: video.id,
      song: matchingSong,
      video,
      idealStartTime: matchingSong.idealStartTime,
      idealEndTime: matchingSong.idealEndTime,
      videoStatus: video.videoStatus,
    } as PowerHourEntry;
  });

  if (pH) {
    if (pH.id) {
      updatePowerHourEntries(pH.id, entries);
    }
    return { entries, pH: { ...pH, entries } };
  }

  return { entries };
}