import * as admin from 'firebase-admin';
// import * as cors from 'cors';
import { Configuration, OpenAIApi, ChatCompletionRequestMessage } from "openai";
import { PowerHourEntry } from '../[models]/models';
import { environment } from '../environment';


const openAIKey = environment.openaiApiKey; 


export const singlePromptPowerHour = async (search: string) => {


  // Fetch the activePrompt ID from Firebase Realtime Database
  const activePromptSnapshot = await admin.database().ref('/activePrompt').once('value');
  const activePromptId = activePromptSnapshot.val();
  console.log(activePromptId)
  if (!activePromptId) {
    throw new Error('Active prompt ID not found');
  }

  // Now, fetch the prompt details using the activePromptId
  const promptSnapshot = await admin.database().ref(`/prompts/${activePromptId}`).once('value');
  const prompt = promptSnapshot.val();
  if (!prompt) {
    throw new Error("No prompt details found for the active prompt");
  }

  // Assuming promptDetails contains the 'search' parameter
  if (!search) {
    throw new Error("No search parameter provided");
  }

  // Initialize OpenAI with your API key
  const configuration = new Configuration({
    apiKey: openAIKey,
  });
  const openai = new OpenAIApi(configuration);

  // Construct the message for OpenAI using the search criteria from your prompt details
  const messages = prompt.messages;

  const processedMessages: ChatCompletionRequestMessage[] = messages.map((message: any) => ({
    role: message.role,
    content: message.content.replace("${search}", search),
  }));

  console.log(processedMessages);

  // Make the call to OpenAI
  const completion = await openai.createChatCompletion({
    messages: processedMessages,
    model: "gpt-4o",
  });

  console.log(completion)
  const content = completion.data.choices[0]?.message?.content;
  if (content) {
    return <PowerHourEntry[]>JSON.parse(content);
  } else {
    throw new Error("Unable to generate Power Hour Entries.");
  }
}



