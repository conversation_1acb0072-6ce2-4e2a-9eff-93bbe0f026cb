/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

// Start writing functions
// https://firebase.google.com/docs/functions/typescript

import * as logger from "firebase-functions/logger";
// import * as cors from 'cors';
import {
  AvailableSteps,
  PHPipelineResult,
  PowerHour,
  Song,
} from "./[models]/models";
import generateSongList from "./power-hour-ai/generate-song-list";
import { songListToVideos } from "./[services]/songs-to-videos-openai/song-list-to-videos-ai";
import replaceSongs from "./power-hour-ai/replace-songs";
import { PHActions, PHOperations } from "./[models]/enums";
import { searchForVideosYT } from "./power-hour-ai/song-list-to-videos-yt";
import { buildPHEntries } from "./power-hour-ai/build-ph-entries";
import { fetchPowerHour, updatePowerHour } from "./[RTDB]/rtdb";
import { songsToVideos } from "./power-hour-ai/songs-to-videos";
import { songsToVideosGoogleSearch } from "./[services]/songs-to-videos-google-search/songs-to-videos-google-search";
import { PowerHourGenerationSteps } from "./[models]/models";
import * as functions from "firebase-functions";
const cors = require("cors")({
  origin: ["http://localhost:3000", "https://power-hour-ai.web.app"],
  credentials: true,
  methods: ["GET", "POST", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
});

// Fix for Gen 1 functions compatibility
const entryPowerHour = functions.https.onRequest(
  async (request: any, response: any) => {
    // Log every incoming request with headers and method
    logger.info(`Request received: ${request.method} ${request.url}`);
    logger.info(`Headers: ${JSON.stringify(request.headers)}`);
    logger.info(`IP: ${request.ip}, Query: ${JSON.stringify(request.query)}`);

    // Handle preflight OPTIONS request directly
    if (request.method === "OPTIONS") {
      response.set(
        "Access-Control-Allow-Origin",
        request.headers.origin || "*"
      );
      response.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
      response.set(
        "Access-Control-Allow-Headers",
        "Content-Type, Authorization, X-Requested-With"
      );
      response.set("Access-Control-Max-Age", "3600");
      response.status(204).send("");
      return;
    }

    cors(request, response, async () => {
      logger.info("Inside CORS middleware handler");
      try {
        // Log raw request body before parsing
        logger.info(`Raw request body: ${request.body}`);

        // Safely parse the request body with error handling
        let payload;
        try {
          // using app with serialized body
          payload = JSON.parse(request.body);
          logger.info("Successfully parsed request body");
        } catch (parseError) {
          logger.error(`Error parsing request body: ${parseError}`);
          logger.error(`Raw body type: ${typeof request.body}`);
          logger.error(`Raw body content: ${request.body}`);

          // Try to handle already parsed body (in case it's already an object)
          if (typeof request.body === "object" && request.body !== null) {
            logger.info(
              "Request body already appears to be an object, using as is"
            );
            payload = request.body;
          } else {
            response
              .status(400)
              .send({ error: "Invalid request body: failed to parse JSON" });
            return;
          }
        }

        // Log the complete payload after parsing
        logger.info(`Parsed payload: ${JSON.stringify(payload)}`);

        const search: string = payload.search;
        const disallow: Song[] = payload.disallow;
        let tasks: PHOperations[] = payload.tasks;
        const action: PHActions = payload.action;
        const count: number = payload.count;
        const powerHourId: string = payload.powerHourId;

        // Validate required fields
        if (!action) {
          logger.warn("Missing action in request");
        }

        if (tasks && !Array.isArray(tasks)) {
          logger.error(`Tasks is not an array: ${JSON.stringify(tasks)}`);
          response.status(400).send({ error: "Tasks must be an array" });
          return;
        }

        if (tasks && tasks.length === 0) {
          logger.warn("Empty tasks array provided");
        }

        // existing logging
        logger.log(`Search: ${search}`);
        logger.log(`Action: ${action}`);
        logger.log(`Tasks: ${JSON.stringify(tasks)}`);
        logger.log(`Id passed: ${powerHourId}`);
        logger.log(`Count: ${count}`);

        let pH: PowerHour = {} as PowerHour;
        if (powerHourId) {
          logger.info(`Fetching power hour with ID: ${powerHourId}`);
          try {
            pH = await fetchPowerHour(powerHourId);
            logger.info(
              `Successfully fetched power hour with ID: ${powerHourId}, hasEntries: ${!!pH.entries}`
            );
          } catch (fetchError) {
            logger.error(`Error fetching power hour: ${fetchError}`);
            response
              .status(500)
              .send({ error: "Failed to fetch power hour data" });
            return;
          }
        }

        const availableSteps: AvailableSteps = {
          [PHOperations.GenerateSongsAI]: (input: unknown) => {
            const { search, disallow } = input as {
              search: string;
              disallow: Song[];
            };
            return generateSongList(search, count, disallow, pH);
          },
          [PHOperations.LocateVideos]: (input: unknown) => {
            const { songs, pH } = input as PHPipelineResult;
            return songsToVideos(songs, pH);
          },
          [PHOperations.LocateVideosAI]: (input: unknown) => {
            const { songs, pH } = input as PHPipelineResult;
            return songListToVideos(songs, pH);
          },
          [PHOperations.LocateVideosYT]: (input: unknown) => {
            const { songs, pH } = input as PHPipelineResult;
            return searchForVideosYT(songs, pH);
          },
          [PHOperations.LocateVideosGoogleSearch]: (input: unknown) => {
            const { songs } = input as PHPipelineResult;
            return songsToVideosGoogleSearch(songs);
          },
          [PHOperations.buildPHEntries]: (input: unknown) => {
            const { songs, videos, pH } = input as PHPipelineResult;
            return buildPHEntries(songs, videos, pH);
          },
          [PHOperations.ReplaceSongsAI]: (input: unknown) => {
            const { songs, pH } = input as PHPipelineResult;
            return replaceSongs(songs, pH);
          },
        };

        if (action && action === PHActions.GeneratePowerHour) {
          logger.info("Setting default tasks for GeneratePowerHour action");
          tasks = [
            PHOperations.GenerateSongsAI,
            PHOperations.LocateVideos,
            PHOperations.buildPHEntries,
          ];
        }

        // Log the tasks that will be executed
        logger.info("Final tasks to execute:", tasks);

        try {
          const pipeline: any = tasks.map((task: PHOperations) => {
            if (availableSteps[task] === undefined) {
              logger.error(`Invalid task provided: ${task}`);
              throw new Error(`Invalid task provided: ${task}`);
            }
            logger.info(`Adding task to pipeline: ${task}`);
            return availableSteps[task];
          });

          // Log the pipeline steps
          logger.info(
            `Starting pipeline execution with steps: ${JSON.stringify(tasks)}`
          );

          // Execute pipeline in sequence
          let currentResult = { search, disallow };

          try {
            for (let i = 0; i < pipeline.length; i++) {
              const step = pipeline[i];
              logger.info(
                `Executing pipeline step ${i + 1}/${pipeline.length}: ${
                  step.name || "unnamed function"
                }`
              );

              // Update power hour with current step information
              if (powerHourId) {
                const currentStep = tasks[i];
                logger.info(
                  `Updating PowerHour ${powerHourId} status: step=${currentStep}, progress=${
                    (i / pipeline.length) * 100
                  }%`
                );
                await updatePowerHour({
                  id: powerHourId,
                  currentStep:
                    currentStep as unknown as PowerHourGenerationSteps,
                  stepProgress: Math.round((i / pipeline.length) * 100),
                  lastUpdateTime: Date.now(),
                });
              }

              try {
                // Log current state before step execution
                logger.info(
                  `Current data keys before step ${i + 1}: ${Object.keys(
                    currentResult
                  ).join(", ")}`
                );

                // Execute the step function
                currentResult = await step(currentResult);

                // Log success
                logger.info(`Step ${i + 1} completed successfully`);
              } catch (stepError) {
                // Log detailed error for this step
                logger.error(
                  `Error in step ${i + 1} (${step.name || "unnamed"})`
                );

                if (stepError instanceof Error) {
                  logger.error(`Error message: ${stepError.message}`);
                  logger.error(`Error stack: ${stepError.stack}`);
                } else {
                  logger.error(
                    `Non-Error thrown: ${JSON.stringify(stepError)}`
                  );
                }

                // Update PowerHour with error information
                if (powerHourId) {
                  logger.info(
                    `Updating PowerHour ${powerHourId} with error information`
                  );
                  const errorMessage =
                    stepError instanceof Error
                      ? stepError.message
                      : JSON.stringify(stepError);

                  const failedStep = tasks[i];
                  await updatePowerHour({
                    id: powerHourId,
                    error: `Error in step: ${failedStep} - ${errorMessage}`,
                    lastUpdateTime: Date.now(),
                  });
                }

                // Rethrow with additional context
                throw {
                  message: `Pipeline step ${i + 1} failed`,
                  step: step.name,
                  originalError:
                    stepError instanceof Error
                      ? stepError.message
                      : JSON.stringify(stepError),
                };
              }
            }

            // Mark as complete
            if (powerHourId) {
              logger.info(`Marking PowerHour ${powerHourId} as complete`);
              await updatePowerHour({
                id: powerHourId,
                currentStep: PowerHourGenerationSteps.Complete,
                stepProgress: 100,
                lastUpdateTime: Date.now(),
              });
            }

            logger.info("Pipeline completed successfully");

            // Send success response
            response.set(
              "Access-Control-Allow-Origin",
              request.headers.origin || "*"
            );
            response.status(200).send(currentResult);
          } catch (pipelineErrorUnknown) {
            // Type casting to access properties safely
            const pipelineError: any = pipelineErrorUnknown;

            logger.error(`Pipeline error: ${JSON.stringify(pipelineError)}`);

            // Update PowerHour with error information before sending response
            if (powerHourId) {
              try {
                await updatePowerHour({
                  id: powerHourId,
                  currentStep: "Error" as unknown as PowerHourGenerationSteps,
                  error: pipelineError.message || "Pipeline execution failed",
                  lastUpdateTime: Date.now(),
                });
              } catch (updateError) {
                logger.error(
                  `Failed to update PowerHour with error status: ${updateError}`
                );
              }
            }

            // Structured error response with details for the client
            response.set(
              "Access-Control-Allow-Origin",
              request.headers.origin || "*"
            );
            response.status(500).send({
              success: false,
              error: "Pipeline execution failed",
              details: pipelineError,
              errorCode: "PIPELINE_FAILURE",
              step: pipelineError.step || "unknown",
              powerHourId: powerHourId || null,
              timestamp: new Date().toISOString(),
            });
          }
        } catch (error) {
          logger.error("Unhandled error in Power Hour Pipeline");
          if (error instanceof Error) {
            logger.error(`Error message: ${error.message}`);
            logger.error(`Error stack: ${error.stack}`);
          } else {
            logger.error(`Non-Error object caught: ${JSON.stringify(error)}`);
          }

          // Update PowerHour with error information for unhandled errors
          if (powerHourId) {
            try {
              await updatePowerHour({
                id: powerHourId,
                currentStep: "Error" as unknown as PowerHourGenerationSteps,
                error:
                  error instanceof Error
                    ? error.message
                    : "Unknown error occurred",
                lastUpdateTime: Date.now(),
              });
            } catch (updateError) {
              logger.error(
                `Failed to update PowerHour with error status: ${updateError}`
              );
            }
          }

          // Structured error response
          response.set(
            "Access-Control-Allow-Origin",
            request.headers.origin || "*"
          );
          response.status(500).send({
            success: false,
            error: "Internal server error",
            message: error instanceof Error ? error.message : "Unknown error",
            errorCode: "INTERNAL_ERROR",
            powerHourId: powerHourId || null,
            timestamp: new Date().toISOString(),
          });
        }
      } catch (error) {
        logger.error("Unhandled error in Power Hour Pipeline");
        if (error instanceof Error) {
          logger.error(`Error message: ${error.message}`);
          logger.error(`Error stack: ${error.stack}`);
        } else {
          logger.error(`Non-Error object caught: ${JSON.stringify(error)}`);
        }

        // Set CORS headers for the error response
        response.set(
          "Access-Control-Allow-Origin",
          request.headers.origin || "*"
        );
        response.status(500).send({
          error: "Internal server error",
          message: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        });
      }
    });
  }
);

export default entryPowerHour;
