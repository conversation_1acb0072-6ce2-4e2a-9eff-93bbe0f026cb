// import * as functions from "firebase-functions";

import TestingEntry from "./[testing]/entry";
import entryPowerHour from "./entry-power-hour";
import checkVideoStatuses from "./yt-video-validator";

// Ensure all exported functions handle CORS correctly
// Functions should have CORS middleware applied as in the individual files

// Oddly this worked to on a basic level
// exports.generatePowerHour = functions.https.onRequest(generatePowerHour);

exports.ytVideoValidator = checkVideoStatuses;
exports.entryPowerHour = entryPowerHour;
exports.TestingEntry = TestingEntry;