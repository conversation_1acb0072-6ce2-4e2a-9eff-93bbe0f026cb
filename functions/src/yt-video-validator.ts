import * as functions from 'firebase-functions';
import { checkVideoStatus } from './[services]/yt-video-validate';


// Cloud Function to check the status of multiple YouTube videos
export const checkVideoStatuses = functions.https.onRequest(async (request, response): Promise<any> => {
  // Use origin from request headers if available
  const origin = request.headers.origin || '*';
  response.set("Access-Control-Allow-Origin", origin);
  response.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  response.set("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
  response.set("Access-Control-Allow-Credentials", "true");
  response.set("Access-Control-Max-Age", "3600");

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    response.status(204).send('');
    return;
  }

  const payload = JSON.parse(request.body);

  const videoIds: string[] = payload.videoIds;
  if (!videoIds || !Array.isArray(videoIds)) {
    return response.status(400).send('Invalid request body. Expected an array of video IDs.');
  }

  try {
    const statusPromises = videoIds.map(id => checkVideoStatus(id));
    const statuses = await Promise.all(statusPromises);
    response.send(statuses);
  } catch (error) {
    console.error('Error processing request:', error);
    response.status(500).send('Internal Server Error');
  }
});

export default checkVideoStatuses;