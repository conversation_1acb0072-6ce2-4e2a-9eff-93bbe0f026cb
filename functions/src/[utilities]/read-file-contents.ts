import * as fs from 'fs/promises';
import * as path from 'path';

export async function readFileContents(relativePath: string) {
  try {
    // Construct an absolute path using __dirname and the relative path
    const filePath = path.join(__dirname, relativePath);
    const data = await fs.readFile(filePath, 'utf8');
    console.log(data);
    // Further processing of data
  } catch (err) {
    console.error('Error reading the file:', err);
  }
}