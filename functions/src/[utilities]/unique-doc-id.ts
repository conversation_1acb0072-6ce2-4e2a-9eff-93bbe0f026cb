export function generateUniqueDocId(title: string, artist: string): string {
  // Clean and shorten inputs
  const cleanTitle = title.trim().replace(/\s+/g, '-').toLowerCase();
  const cleanArtist = artist.trim().replace(/\s+/g, '-').toLowerCase();

  // Combine and hash for uniqueness
  const combinedString = `${cleanTitle}-${cleanArtist}`;
  const hash = btoa(combinedString); // Base64 encoding provides short representation

  // Truncate to avoid exceeding Firestore document ID length limit
  const MAX_ID_LENGTH = 255;
  const docId = hash.substring(0, Math.min(hash.length, MAX_ID_LENGTH));

  // Add prefix to avoid name collisions
  const PREFIX = 'video-cache-'; // Replace with your desired prefix
  return `${PREFIX}${docId}`;
}
