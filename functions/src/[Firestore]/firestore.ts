import { logger } from "firebase-functions/v2";
import { PowerHour, PowerHourEntry, Song, Video } from "../[models]/models";
import { Firestore } from "../firebase";

const db = Firestore;

export const fetchPowerHour = async (powerHourId: string): Promise<PowerHour> => {
  const docRef = db.collection('power-hours').doc(powerHourId);
  const doc = await docRef.get();
  if (doc.exists) {
    console.log("Document data:", doc.data());
    return doc.data() as PowerHour;
  } else {
    console.log("No such document!");
    return {} as PowerHour;
  }
}

export const updatePowerHour = async (powerHour: Partial<PowerHour>): Promise<Partial<PowerHour>> => {
  if (!powerHour?.id) {
    logger.error("PowerHour ID is required. Couldn't updatePowerHour in Firestore from Cloud Functions");
    return powerHour;
  }
  const docRef = db.collection('power-hours').doc(powerHour.id);
  await docRef.update(powerHour);
  return powerHour;
}

export const updatePowerHourSongs = async (powerHourId: string, songs: Song[]): Promise<Song[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldn't updatePowerHourSongs in Firestore from Cloud Functions");
    return songs;
  }
  const docRef = db.collection('power-hours').doc(powerHourId);
  await docRef.update({ songs: songs });
  return songs;
}

export const updatePowerHourVideos = async (powerHourId: string, videos: Video[]): Promise<Video[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldn't updatePowerHourVideos in Firestore from Cloud Functions");
    return videos;
  }
  const docRef = db.collection('power-hours').doc(powerHourId);
  await docRef.update({ videos: videos });
  return videos;
}

export const updatePowerHourEntries = async (powerHourId: string, entries: PowerHourEntry[]): Promise<PowerHourEntry[]> => {
  if (!powerHourId) {
    logger.error("PowerHour ID is required. Couldn't updatePowerHourEntries in Firestore from Cloud Functions");
    return entries;
  }
  const docRef = db.collection('power-hours').doc(powerHourId);
  await docRef.update({ entries: entries });
  return entries;
}
