import { logger } from "firebase-functions/v1";
import { Song, Video } from "../../[models]/models";
import axios from "axios";


const googleSearchApiKey = process.env.GOOGLE_SEARCH_API_KEY;
const cseID = process.env.GOOGLE_SEARCH_CSE_ID;

export const songsToVideosGoogleSearch = async (songList: Partial<Song>[]): Promise<any> => {
  if (!songList) {
    logger.error("No Song List");
  }

  songList.forEach(song => song.searchableDescription = (song?.title + " By " + song?.artist));

  let videos: Video[] = [];

  for (let song of songList) {

    const query = song.searchableDescription;

    const url = 'https://www.googleapis.com/customsearch/v1';
    const params = {
      key: googleSearchApiKey,
      cx: cseID,
      q: query
    };

    try {
      const response = await axios.get(url, { params });
      const video = createVideoObject(song, response)
      videos.push(video);

    } catch (error: any) {
      console.error('Error during API request:', error.message);
      return null;
    }
  }
}

function createVideoObject(song: Partial<Song>, searchResult: any): Video {
  // Extract video ID from the URL
  const videoIdRegex = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/;
  const match = searchResult.link.match(videoIdRegex);
  const videoId = match ? match[1] : undefined;
  if (!videoId) {
    logger.error("No match in video id regex");
  }

  const video: Video = {
    id: videoId || '',
    songId: song.id ?? (song.title + "_" + song.artist).toLowerCase(),
    artist: song.artist,
    title: searchResult.title,
    description: searchResult.snippet,
    url: searchResult.link,
    thumbnail: searchResult.pagemap.cse_image[0].src,
    provider: 'YouTube',
    videoStatus: { videoId, status: 'Available', timeVerified: Date.now() }
  };

  return video;
}


// {
//   "kind": "customsearch#search",
//   "url": {
//       "type": "application/json",
//       "template": "https://www.googleapis.com/customsearch/v1?q={searchTerms}&num={count?}&start={startIndex?}&lr={language?}&safe={safe?}&cx={cx?}&sort={sort?}&filter={filter?}&gl={gl?}&cr={cr?}&googlehost={googleHost?}&c2coff={disableCnTwTranslation?}&hq={hq?}&hl={hl?}&siteSearch={siteSearch?}&siteSearchFilter={siteSearchFilter?}&exactTerms={exactTerms?}&excludeTerms={excludeTerms?}&linkSite={linkSite?}&orTerms={orTerms?}&dateRestrict={dateRestrict?}&lowRange={lowRange?}&highRange={highRange?}&searchType={searchType}&fileType={fileType?}&rights={rights?}&imgSize={imgSize?}&imgType={imgType?}&imgColorType={imgColorType?}&imgDominantColor={imgDominantColor?}&alt=json"
//   },
//   "queries": {
//       "request": [
//           {
//               "title": "Google Custom Search - Levels - Avicii",
//               "totalResults": "277000",
//               "searchTerms": "Levels - Avicii",
//               "count": 10,
//               "startIndex": 1,
//               "inputEncoding": "utf8",
//               "outputEncoding": "utf8",
//               "safe": "off",
//               "cx": "c76cb4b04b88b4b0d"
//           }
//       ],
//       "nextPage": [
//           {
//               "title": "Google Custom Search - Levels - Avicii",
//               "totalResults": "277000",
//               "searchTerms": "Levels - Avicii",
//               "count": 10,
//               "startIndex": 11,
//               "inputEncoding": "utf8",
//               "outputEncoding": "utf8",
//               "safe": "off",
//               "cx": "c76cb4b04b88b4b0d"
//           }
//       ]
//   },
//   "context": {
//       "title": "Youtube"
//   },
//   "searchInformation": {
//       "searchTime": 0.25425,
//       "formattedSearchTime": "0.25",
//       "totalResults": "277000",
//       "formattedTotalResults": "277,000"
//   },
//   "items": [
//       {
//           "kind": "customsearch#result",
//           "title": "Avicii - Levels - YouTube",
//           "htmlTitle": "<b>Avicii</b> - <b>Levels</b> - YouTube",
//           "link": "https://www.youtube.com/watch?v=_ovdm2yX4MA",
//           "displayLink": "www.youtube.com",
//           "snippet": "Nov 29, 2011 ... https://avicii.lnk.to/levels10yrs In loving memory of Tim "Avicii" Bergling (1989-2018). Please watch "Levels" in Avicii's final performance...",
//           "htmlSnippet": "Nov 29, 2011 <b>...</b> https://<b>avicii</b>.lnk.to/levels10yrs In loving memory of Tim "<b>Avicii</b>" Bergling (1989-2018). Please watch "<b>Levels</b>" in <b>Avicii&#39;s</b> final performance&nbsp;...",
//           "cacheId": "00ZY9Y51fFsJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=_ovdm2yX4MA",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=_ovdm2yX4MA",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcT8TJ-94pk5L0iFeze5LnqYpRbPB2N_GWkHWCV7uaLAH8_nWi7oed2jy1A",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=_ovdm2yX4MA&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=_ovdm2yX4MA",
//                       "og:url": "https://www.youtube.com/watch?v=_ovdm2yX4MA"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/cHHLHGNpCSA/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLBYF6k4o9yvlzw1SvTmIwIoiXM1jA"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Avicii Tribute Concert - Levels - YouTube",
//           "htmlTitle": "<b>Avicii</b> Tribute Concert - <b>Levels</b> - YouTube",
//           "link": "https://www.youtube.com/watch?v=5l7unG7xGD8",
//           "displayLink": "www.youtube.com",
//           "snippet": "Dec 13, 2019 ... Avicii Tribute Concert celebrates the life and music of Tim Bergling - AVICII - live at Friends Arena in Stockholm, Sweden together with...",
//           "htmlSnippet": "Dec 13, 2019 <b>...</b> <b>Avicii</b> Tribute Concert celebrates the life and music of Tim Bergling - <b>AVICII</b> - live at Friends Arena in Stockholm, Sweden together with&nbsp;...",
//           "cacheId": "6QsfLnfI1t4J",
//           "formattedUrl": "https://www.youtube.com/watch?v=5l7unG7xGD8",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=5l7unG7xGD8",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn3.gstatic.com/images?q=tbn:ANd9GcThyHwR14mscTrDExAIkApiu_Jcid0mdeksYLNDbi6boaYPrfKkN-q8lnR2",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=5l7unG7xGD8&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=5l7unG7xGD8",
//                       "og:url": "https://www.youtube.com/watch?v=5l7unG7xGD8"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/gdsUKphmB3Y/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLADy6CeqVMSSaDC-M9G43x4aHWodw"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Levels (Original Version) - YouTube",
//           "htmlTitle": "<b>Levels</b> (Original Version) - YouTube",
//           "link": "https://www.youtube.com/watch?v=wDuoOapZ9Z0",
//           "displayLink": "www.youtube.com",
//           "snippet": "Jul 31, 2018 ... Provided to YouTube by Universal Music Group Levels (Original Version) · Avicii Levels ℗ 2011 Avicii Music AB, Under exclusive license to...",
//           "htmlSnippet": "Jul 31, 2018 <b>...</b> Provided to YouTube by Universal Music Group <b>Levels</b> (Original Version) · <b>Avicii Levels</b> ℗ 2011 <b>Avicii</b> Music AB, Under exclusive license to&nbsp;...",
//           "cacheId": "dHDVQcmoI18J",
//           "formattedUrl": "https://www.youtube.com/watch?v=wDuoOapZ9Z0",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=wDuoOapZ9Z0",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSYELDtMBYlMq4i-MqdS1-v-iz1JXM3J7s-acnJPHWi0xtVE1BE-szzywml",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=wDuoOapZ9Z0&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=wDuoOapZ9Z0",
//                       "og:url": "https://www.youtube.com/watch?v=wDuoOapZ9Z0"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/OjpX8ILe2N4/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLCE09SDXo_qBO4ETMRscauOmMcwEA"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Joe: Republicans last night weren't happy at the world Trump ...",
//           "htmlTitle": "Joe: Republicans last night weren&#39;t happy at the world Trump ...",
//           "link": "https://www.youtube.com/watch?v=f4tvFhwI6_A",
//           "displayLink": "www.youtube.com",
//           "snippet": "5 days ago ... The Morning Joe panel continues its discussion of President Biden's State of the Union, and the response from Republican lawmakers.",
//           "htmlSnippet": "5 days ago <b>...</b> The Morning Joe panel continues its discussion of President Biden&#39;s State of the Union, and the response from Republican lawmakers.",
//           "cacheId": "mlOFXFXfHRUJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=f4tvFhwI6_A",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=f4tvFhwI6_A",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTLPqRvBMs-KosR3r7LLd-aFkcUVtwCMvj-MXGqrGsTS7DiXapQc6ohL0o",
//                       "width": "139",
//                       "height": "78"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=f4tvFhwI6_A&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=f4tvFhwI6_A",
//                       "og:url": "https://www.youtube.com/watch?v=f4tvFhwI6_A"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/OWES2vPMV8o/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLBcJ91N60eOlNkluPHFiqMZEx3fag"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Avicii- Levels (Audio) - YouTube",
//           "htmlTitle": "<b>Avicii</b>- <b>Levels</b> (Audio) - YouTube",
//           "link": "https://www.youtube.com/watch?v=EgxHqMpK1Nw",
//           "displayLink": "www.youtube.com",
//           "snippet": "Jan 10, 2012 ... \"Levels\" (stylized as \"Le7els\") is a song by Swedish house producer and DJ Avicii. It was released on October 28, 2011. The song was written...",
//           "htmlSnippet": "Jan 10, 2012 <b>...</b> &quot;<b>Levels</b>&quot; (stylized as &quot;Le7els&quot;) is a song by Swedish house producer and DJ <b>Avicii</b>. It was released on October 28, 2011. The song was written&nbsp;...",
//           "cacheId": "CEdl5-rjZQkJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=EgxHqMpK1Nw",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=EgxHqMpK1Nw",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcS_QszBHXL-ojOkPrVrNlrCR1N0hC2uFgW2JSQRZ7f3OkYLP-Z_7beQHgw",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=EgxHqMpK1Nw&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=EgxHqMpK1Nw",
//                       "og:url": "https://www.youtube.com/watch?v=EgxHqMpK1Nw"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/5y_KJAg8bHI/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLAj2OJNIef5UfuexKiMT83saoAtLg"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Avicii - Levels (Live In Ibiza, 2016) - YouTube",
//           "htmlTitle": "<b>Avicii</b> - <b>Levels</b> (Live In Ibiza, 2016) - YouTube",
//           "link": "https://www.youtube.com/watch?v=MPwWLBIywrw",
//           "displayLink": "www.youtube.com",
//           "snippet": "Oct 28, 2021 ... Live version of "Levels" at Avicii's final performance (Ibiza, 2016-08-28). https://avicii.lnk.to/levels10yrs In loving memory of Tim...",
//           "htmlSnippet": "Oct 28, 2021 <b>...</b> Live version of "<b>Levels</b>" at <b>Avicii&#39;s</b> final performance (Ibiza, 2016-08-28). https://<b>avicii</b>.lnk.to/levels10yrs In loving memory of Tim&nbsp;...",
//           "cacheId": "MXBUlRd6_3IJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=MPwWLBIywrw",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=MPwWLBIywrw",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcTgQBN67mRD0_UfNv_RbA8OASwL8BFh76Qj2HkzoPKQqn49Ay-Uuu1OfQ8",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "VideoObject": [
//                   {}
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=MPwWLBIywrw&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=MPwWLBIywrw",
//                       "og:url": "https://www.youtube.com/watch?v=MPwWLBIywrw"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/CW0abEuuFwc/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLA4SvXc2hoqAq_RErV58Ln_e_IrnQ"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Avicii 'Levels' Skrillex Remix [FULL] - YouTube",
//           "htmlTitle": "<b>Avicii</b> &#39;<b>Levels</b>&#39; Skrillex Remix [FULL] - YouTube",
//           "link": "https://www.youtube.com/watch?v=MITA4FhVjDc",
//           "displayLink": "www.youtube.com",
//           "snippet": "Dec 23, 2011 ... iTunes: https://itunes.apple.com/us/album/levels-skrillex-remix/id487945995?i=487946086 Listen on Spotify:...",
//           "htmlSnippet": "Dec 23, 2011 <b>...</b> iTunes: https://itunes.apple.com/us/album/<b>levels</b>-skrillex-remix/id487945995?i=487946086 Listen on Spotify:&nbsp;...",
//           "cacheId": "jlts-AVCYikJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=MITA4FhVjDc",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=MITA4FhVjDc",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcSLE4Gdsdp0hTZQOcEJjoJObVAZeb3lK8bjlWinSxLMc0RQa6fV7fbt0DCY",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=MITA4FhVjDc&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=MITA4FhVjDc",
//                       "og:url": "https://www.youtube.com/watch?v=MITA4FhVjDc"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/Ua0KpfJsxKo/hq720.jpg?sqp=-oaymwE7CK4FEIIDSFryq4qpAy0IARUAAAAAGAElAADIQj0AgKJD8AEB-AH-CYAC0AWKAgwIABABGGUgZShlMA8=&rs=AOn4CLB7pLuaHiaYYECG78ug-_xdPDb6-A"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Levels - Avicii (Lyrics) - YouTube",
//           "htmlTitle": "<b>Levels</b> - <b>Avicii</b> (Lyrics) - YouTube",
//           "link": "https://www.youtube.com/watch?v=_GenVbVFdYw",
//           "displayLink": "www.youtube.com",
//           "snippet": "Aug 18, 2020 ... Find Avicii on: Lyrics: \"Levels\" https://pillowlyrics.com/levels-avicii/ Levels - Avicii (Lyrics) Lyrics video for \"Levels\" by Avicii.",
//           "htmlSnippet": "Aug 18, 2020 <b>...</b> Find Avicii on: Lyrics: &quot;Levels&quot; https://pillowlyrics.com/<b>levels</b>-<b>avicii</b>/ <b>Levels</b> - <b>Avicii</b> (Lyrics) Lyrics video for &quot;Levels&quot; by Avicii.",
//           "cacheId": "U4eUJEsqKZ0J",
//           "formattedUrl": "https://www.youtube.com/watch?v=_GenVbVFdYw",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=_GenVbVFdYw",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn2.gstatic.com/images?q=tbn:ANd9GcRunTYzCfV_Aqy14e73Z-7SqPYEsTkTShC3N0hXUXBE7Ta6mZZKoqydT0s",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=_GenVbVFdYw&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=_GenVbVFdYw",
//                       "og:url": "https://www.youtube.com/watch?v=_GenVbVFdYw"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/QbxUro8BIWE/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLAllRvcCt8z-kqWYn0X_8IGKKMdMQ"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "LEVELS / Avicii - SYMPHONIACS (violin, cello, piano and electronic ...",
//           "htmlTitle": "<b>LEVELS</b> / <b>Avicii</b> - SYMPHONIACS (violin, cello, piano and electronic ...",
//           "link": "https://www.youtube.com/watch?v=SBdTYXnS6To",
//           "displayLink": "www.youtube.com",
//           "snippet": "May 17, 2017 ... Symphoniacs Version of Levels (originally performed by Avicii) Listen on APPLE MUSIC: http://apple.co/2YxPpUV SPOTIFY:...",
//           "htmlSnippet": "May 17, 2017 <b>...</b> Symphoniacs Version of <b>Levels</b> (originally performed by <b>Avicii</b>) Listen on APPLE MUSIC: http://apple.co/2YxPpUV SPOTIFY:&nbsp;...",
//           "cacheId": "46Yns7oT7EIJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=SBdTYXnS6To",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=SBdTYXnS6To",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSaVhljmyH1XU9SUjbFjbqqK6vBz-gXQ45oFYL8oqe3RtxkO4O2uwLsNBQx",
//                       "width": "299",
//                       "height": "168"
//                   }
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=SBdTYXnS6To&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=SBdTYXnS6To",
//                       "og:url": "https://www.youtube.com/watch?v=SBdTYXnS6To"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/idzzowF5zDw/hq720.jpg?sqp=-oaymwEhCK4FEIIDSFryq4qpAxMIARUAAAAAGAElAADIQj0AgKJD&rs=AOn4CLCDRrQcnkHhtALtai_Cwn21rZRDgQ"
//                   }
//               ]
//           }
//       },
//       {
//           "kind": "customsearch#result",
//           "title": "Booba Cartoon - Levels (Avicii cover) - Music video - Booba Sings ...",
//           "htmlTitle": "Booba Cartoon - <b>Levels</b> (<b>Avicii</b> cover) - Music video - Booba Sings ...",
//           "link": "https://www.youtube.com/watch?v=ezxZdcA5Dso",
//           "displayLink": "www.youtube.com",
//           "snippet": "Nov 8, 2021 ... Booba sings – Levels [Avicii cover]. Available on all music platforms: http://lnk.to/BoobaLevels ▻ Click here to watch more Booba:...",
//           "htmlSnippet": "Nov 8, 2021 <b>...</b> Booba sings – <b>Levels</b> [<b>Avicii</b> cover]. Available on all music platforms: http://lnk.to/BoobaLevels ▻ Click here to watch more Booba:&nbsp;...",
//           "cacheId": "N24Xovass8UJ",
//           "formattedUrl": "https://www.youtube.com/watch?v=ezxZdcA5Dso",
//           "htmlFormattedUrl": "https://www.youtube.com/watch?v=ezxZdcA5Dso",
//           "pagemap": {
//               "cse_thumbnail": [
//                   {
//                       "src": "https://encrypted-tbn1.gstatic.com/images?q=tbn:ANd9GcRS9r0DgL3n9NO1MwP4bXnDXhEQocY5YSynijgvu-fGhfIkjwaymbwnjtVA",
//                       "width": "168",
//                       "height": "300"
//                   }
//               ],
//               "VideoObject": [
//                   {}
//               ],
//               "metatags": [
//                   {
//                       "apple-itunes-app": "app-id=*********, app-argument=https://m.youtube.com/watch?v=ezxZdcA5Dso&referring_app=com.apple.mobilesafari-smartbanner, affiliate-data=ct=smart_app_banner_polymer&pt=9008",
//                       "theme-color": "rgba(0, 0, 0, 0)",
//                       "viewport": "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,",
//                       "twitter:url": "https://www.youtube.com/watch?v=ezxZdcA5Dso",
//                       "og:url": "https://www.youtube.com/watch?v=ezxZdcA5Dso"
//                   }
//               ],
//               "cse_image": [
//                   {
//                       "src": "https://i.ytimg.com/vi/1qy5udfous8/oar2.jpg?sqp=-oaymwEYCJUDENAFSFqQAgHyq4qpAwcIARUAAIhC&rs=AOn4CLC92bbPj-BtgclMUDBW69U7fakKzw"
//                   }
//               ]
//           }
//       }
//   ]
// }