import axios from 'axios';
import { logger } from 'firebase-functions/v1';
import { environment } from '../environment';


const YOUTUBE_API_KEY = environment.openaiApiKey;

// Define a type for the response structure to improve type checking
export type YouTubeSearchResponse = {
  items: Array<{
    id: {
      videoId: string;
    };
    snippet: {
      title: string;
      description: string;
      thumbnails: {
        default: {
          url: string;
        };
        medium: {
          url: string;
        };
        high: {
          url: string;
        };
      };
    };
  }>;
};

export async function searchForMusicVideo(songTitle: string, artistName: string): Promise<YouTubeSearchResponse> {
  console.log("Attempting YT Lookup")
  const apiKey = YOUTUBE_API_KEY;
  const searchQuery = `${songTitle} ${artistName}`;

  try {
    const response = await axios.get<YouTubeSearchResponse>('https://www.googleapis.com/youtube/v3/search', {
      params: {
        part: 'snippet',
        q: searchQuery,
        type: 'video',
        videoCategoryId: '10', // Category ID for Music
        maxResults: 1, // Adjust based on how many results you want
        key: apiKey,
      },
    });

    return response.data;

  } catch (error) {
    logger.error('Error during YouTube API search:', error);
  }
  return {} as YouTubeSearchResponse;
}

