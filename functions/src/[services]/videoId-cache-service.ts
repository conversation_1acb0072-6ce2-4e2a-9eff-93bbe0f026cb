import * as admin from 'firebase-admin';
import { generateUniqueDocId } from '../[utilities]/unique-doc-id';
import { Video } from '../[models]/models';

const db: admin.firestore.Firestore = admin.firestore();
const collectionName = "cache";
const cacheTtl = 3600; // 1 hour

interface CachedItem {
  data: Video;
  cachedAt: Date;
}

export async function get(title: string, artist: string): Promise<Video | null> {
  const querySnapshot = await db.collection(collectionName)
    .where('data.title', '==', title)
    .where('data.artist', '==', artist)
    .get();

  const cachedItems = querySnapshot.docs.map(doc => doc.data() as CachedItem);
  if (cachedItems.length === 0) {
    return null;
  }

  const cachedItem = cachedItems.find(item => !isStale(item.cachedAt));
  if (cachedItem) {
    return cachedItem.data;
  }

  // If all cached items are stale, remove them and return null
  await querySnapshot.docs.forEach(doc => doc.ref.delete());
  return null;
}

export async function set(title: string, artist: string, videoInfo: Video): Promise<void> {
  const docId = generateUniqueDocId(title, artist); // Implement a function to generate unique doc IDs
  const cachedItem: CachedItem = {
    data: videoInfo,
    cachedAt: new Date(),
  };

  await db.collection(collectionName).doc(docId).set(cachedItem);
}

function isStale(cachedAt: Date): boolean {
  const now = new Date();
  const diff = Math.floor((now.getTime() - cachedAt.getTime()) / 1000);
  return diff > cacheTtl;
}

export async function remove(videoId: string): Promise<void> {
  await db.collection(collectionName).doc(videoId).delete();
}

export async function clear(): Promise<void> {
  const snapshot = await db.collection(collectionName).get();
  snapshot.forEach(doc => doc.ref.delete());
}

