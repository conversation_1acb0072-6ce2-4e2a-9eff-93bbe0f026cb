import { PowerH<PERSON>, Song, Video } from "../../[models]/models";
import { songListToVideosPrompt } from "../../[prompts]/song-list-to-videos";
import { logger } from "firebase-functions/v1";
import { environment } from "../../environment";
import { Configuration, OpenAIApi, ChatCompletionRequestMessage } from "openai";

const openAIKey: string = environment.openaiApiKey!;
console.log("openAIKey: ", openAIKey);

export const songListToVideos = async (
  songList: Partial<Song>[], 
  pH?: Partial<PowerHour>
): Promise<{ videos: Video[]; pH?: Partial<PowerHour> }> => {
  // Transform the song list into a friendly format for the prompt
  const friendlySongList: string[] = songList.map(
    (song) => `id: ${song?.id} song title: ${song?.title} artist: ${song?.artist}`
  );

  // Generate the prompt from the song list
  const messages: ChatCompletionRequestMessage[] = songListToVideosPrompt(friendlySongList);

  // Configure OpenAI API
  const configuration = new Configuration({
    apiKey: openAIKey,
  });
  const openai = new OpenAIApi(configuration);

  try {
    // Call the OpenAI Chat Completion API
    const completion = await openai.createChatCompletion({
      messages,
      model: "gpt-4",
    });

    // Parse and log the response
    const content = completion.data.choices[0]?.message?.content;
    if (content) {
      const data = JSON.parse(content);

      logger.log("completion: ", data);
      logger.info("Generate Videos complete, # of videos generated: ", data.videos.length);

      if (!data.videos) {
        throw new Error("No entries were generated.");
      }

      return { videos: data.videos, pH: { ...pH, videos: data.videos } };
    } else {
      throw new Error("No content after generating Videos.");
    }
  } catch (error: any) {
    logger.error("Error generating Videos: ", error);
    throw new Error(`Error generating Videos: ${error.message}`);
  }
};
