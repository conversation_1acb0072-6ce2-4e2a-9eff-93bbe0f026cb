
import { VideoStatus } from "../[models]/models";
import { environment } from "../environment";

const YOUTUBE_API_KEY = environment.openaiApiKey;

// Function to check the status of a single YouTube video
export async function checkVideoStatus(videoId: string): Promise<VideoStatus> {

  try {
    const response = await fetch(`https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=status&key=${YOUTUBE_API_KEY}`);
    const data = await response.json();
    if (!data) {
      throw new Error("No data returned from YouTube API");
    }

    if (data.items.length === 0) {
      return { videoId, status: 'Not Found', timeVerified: Date.now() };
    }

    const status = data.items[0].status;
    if (status.privacyStatus === 'public' && status.uploadStatus === 'processed') {
      return { videoId, status: 'Available', timeVerified: Date.now() };
    } else {
      return { videoId, status: `Unavailable - ${status.privacyStatus}`, timeVerified: Date.now() };
    }
  } catch (error) {
    console.error('Error fetching video status:', error);
    return { videoId, status: 'Error checking status', timeVerified: Date.now() };
  }
}