import { ChatCompletionRequestMessage } from "openai";

export const songListToVideosPrompt = (songList: string[]): ChatCompletionRequestMessage[] => {
  return [
    {
      role: "system",
      content:
        "You will be provided a list of songs to match to videos on youtube.",
    },
    {
      role: "system",
      content: `The songs are as follows: '${songList.join("', '")}'`,
    },
    // dynamically add the Power Hour Entry format? Maybe just read the models.ts file in as a string and add it to the messages?
    {
      role: "system",
      content: `You need to create a Video entry for each song in JSON format. Use these interfaces to build entries for each song:
      export interface Video {
        id: string; // videoId from youtube
        songId: string; // songId from the song list, should match exactly to the song list
        artist?:string, // artist of the song, should match exactly to the song list
        url: string; // youtube video url
        thumbnail: string; // youtube video thumbnail
      }
      `,
    },
    {
      role: "system",
      content: `The result should be an array of Video objects with a length of ${songList.length} and look like this:
      { 
        videos: [
          {
            id: string; // videoId from youtube
            songId: string; // songId from the song list, should match exactly to the song list
            artist?:string, // artist of the song, should match exactly to the song list
            url: string; // youtube video url
            thumbnail: string; // youtube video thumbnail
          },
        ...
        ]
      }`,
    },
    {
      role: "system",
      content: `The songId should match the Id of the song from the list of songs.`,
    },
  ];
};
