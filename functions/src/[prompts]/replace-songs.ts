import { ChatCompletionRequestMessage, ChatCompletionRequestMessageRoleEnum } from "openai"
import { Song } from "../[models]/models"


export const replaceSongsPrompt = (search: string, songsToReplace: Song[]): { messages: ChatCompletionRequestMessage[] } => {
  return {
    messages: [
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `The user did not like ${songsToReplace.length} songs in the Power Hour playlist. You need to replace them with new song that relates to the original search term.`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `You need to generate a list of ${songsToReplace.length} new songs, to replace ${JSON.stringify(songsToReplace)} based on the original search term '${search}'.`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `Do not pick the same songs!`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `Replace exactly ${songsToReplace.length} songs. The output should be in JSON format matching this structure:
        interface Song {
          id:string;
          title: string;
          artist: string;
          genre: MusicGenre;
          album?: string; 
          year?: number; 
          durationInSeconds: number;
          idealStartTime?: number;
          idealEndTime?: number;
        }
        
        
        enum MusicGenre {
          Pop = "Pop",
          Rock = "Rock",
          HipHop = "Hip-Hop/Rap",
          Jazz = "Jazz",
          Blues = "Blues",
          Country = "Country",
          Electronic = "Electronic/Dance",
          Classical = "Classical",
          Reggae = "Reggae",
          RnB = "R&B (Rhythm and Blues)",
          Folk = "Folk",
          Punk = "Punk",
          Metal = "Metal",
          Soul = "Soul",
          Funk = "Funk",
          Gospel = "Gospel",
          Ska = "Ska",
          Indie = "Indie",
          Alternative = "Alternative",
          Latin = "Latin",
          World = "World",
          Ambient = "Ambient",
          Reggaeton = "Reggaeton",
          Techno = "Techno",
          Disco = "Disco",
          House = "House",
          Dubstep = "Dubstep",
          Rap = "Rap",
          EDM = "EDM (Electronic Dance Music)",
          KPop = "K-Pop",
          JPop = "J-Pop",
          ClassicalCrossover = "Classical Crossover",
          NewWave = "New Wave",
          Psychedelic = "Psychedelic",
          Grunge = "Grunge",
          HardRock = "Hard Rock",
          Experimental = "Experimental",
          Acoustic = "Acoustic",
          BluesRock = "Blues Rock",
          PopPunk = "Pop Punk",
          Bluegrass = "Bluegrass",
          Cajun = "Cajun",
          Flamenco = "Flamenco",
          Salsa = "Salsa",
          Afrobeat = "Afrobeat",
          Soca = "Soca",
          Tejano = "Tejano",
          Zydeco = "Zydeco",
          Kizomba = "Kizomba",
          AvantGarde = "Avant-Garde"
        }.`
      },
      {
        role: "system",
        content: `The id should be a unique identifier for the song, it can be something like {title}-{artist}. The durationInSeconds should be the length of the song in seconds.`
      },
      {
        role: "system",
        content: `idealStartTime and idealEndTime should be a point in time in seconds, where idealStartTime is the time in the song where a 60 second clip should start, and idealEndTime is the time in the song where the 60 second clip should end.`
      },
    ]
  }
}
