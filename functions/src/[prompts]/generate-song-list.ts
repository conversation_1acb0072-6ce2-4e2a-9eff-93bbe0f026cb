import { logger } from 'firebase-functions/v1';
import { Song } from '../[models]/models';
import { atLeastAFullMinuteRemaining, noDuplicateSongs, sampleSongLists } from './snippets';
import { ChatCompletionRequestMessage, ChatCompletionRequestMessageRoleEnum } from 'openai';

export const generateSongListPrompt = async (
  search: string, 
  disallow: Song[], 
  count: number
): Promise<{ messages: ChatCompletionRequestMessage[] }> => {
  logger.info(`Generating song list prompt for ${search} with ${disallow?.length} disallowed songs and a count of ${count}`);

  let disallowPrompt: ChatCompletionRequestMessage = {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `No Songs are disallowed.`
  };

  if (disallow.length > 0) {
    disallowPrompt = {
      role: ChatCompletionRequestMessageRoleEnum.System,
      content: `Do NOT use any songs that match the following: ${disallow.map(s => `${s.title} ${s.artist}`).join(", ")}`
    };
  }

  return {
    messages: [
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `You are creating a special Power Hour playlist. It often will contain 60 songs but there are certaion cases where it will be less. I will provide you with a number and that should be the number of songs you generate. You need to generate a list of ${count} songs based on a search term.
        I will list some requirements for the song list you create. You need to follow these requirements.`
      },
      ...atLeastAFullMinuteRemaining,
      disallowPrompt,
      ...noDuplicateSongs,
      ...sampleSongLists,
      // ...mustHaveCorrectNumberOfSongs,
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `The id should be a unique identifier for the song, it can be something like "shapeofyou-edsheeran" in lowercase with no spaces.`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `The album and year are optional, but if you have them, include them.`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `idealStartTime and idealEndTime should be points in time in seconds, where idealStartTime is the time in the song where a 60-second clip should start, and idealEndTime is the time in the song where the 60-second clip should end.`
      },
      {
        role: ChatCompletionRequestMessageRoleEnum.System,
        content: `Finally, the output should be a JSON array of songs. Do not include any other text in the output.`
      }
    ]
  };
};
