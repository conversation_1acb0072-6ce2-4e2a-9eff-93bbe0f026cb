import { ChatCompletionRequestMessage } from "openai";

export const songsToJSONPrompt = (
  songsAsList: string
): { messages: ChatCompletionRequestMessage[] } => {
  return {
    messages: [
      {
        role: "system",
        content: `You are a helpful assistant that converts text into structured JSON.`
      },
      {
        role: "user",
        content: `Here are the songs for a Power Hour: ${songsAsList}

Please convert these songs to a valid JSON object following the structure below. 

Your response MUST be ONLY valid JSON without any additional text, explanation or markdown formatting.
The response must start with '{' and end with '}' and be parseable by JSON.parse().

Here's the Song interface:

{
  "songs": [
    {
      "id": "song-1",
      "title": "Song Title",
      "artist": "Artist Name",
      "genre": "Pop",
      "album": "Album Name",
      "year": 2020,
      "durationInSeconds": 180,
      "idealStartTime": 0,
      "idealEndTime": 60
    }
  ]
}

For each song, provide all the fields you can identify. Use "song-1", "song-2", etc. for IDs.
If year is not specified, make a reasonable guess.
Set idealStartTime to 0 and idealEndTime to 60 for all songs.
Set durationInSeconds to 240 if unknown.`
      }
    ],
  };
};
