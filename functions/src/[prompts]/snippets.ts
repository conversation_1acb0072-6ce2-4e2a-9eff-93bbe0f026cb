import { epicMovieSoundtracks } from "../sample-power-hours/epic-movie-soundtracks"
import { HipHop } from "../sample-power-hours/hip-hip"
import { NinetiesPowerHour } from "../sample-power-hours/nineties"
import { ChatCompletionRequestMessageRoleEnum } from 'openai'

export const atLeastAFullMinuteRemaining = [
  {
    role: ChatCompletionRequestMessageRoleEnum.User,
    content: `Each power hour song should have at least a full minute remaining.`
  }
]

export const noDuplicateSongs = [
  {
    role: ChatCompletionRequestMessageRoleEnum.User,
    content: `Do not pick the same songs!`
  }
]


export const sampleSongLists = [
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `Here is a sample list of songs for a Hip Hop Power Hour: ${HipHop}`
  },
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `Here is a sample list of songs for a Nineties Power Hour: ${NinetiesPowerHour}`
  },
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `Here is a sample list of songs for an Epic Movie Soundtrack Power Hour: ${epicMovieSoundtracks}`
  }
]


export const mustHaveCorrectNumberOfSongs = [
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `You must have the correct number of songs requested. Any less than the requested number of songs will result in a failed power hour.`
  },
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `VERY IMPORTANT: You must have the correct number of songs requested. Any less than the requested number of songs will result in a failed power hour.`
  },
  {
    role: ChatCompletionRequestMessageRoleEnum.System,
    content: `VERY IMPORTANT: Once you finish looking, double check that you have the correct number of songs requested. Any less than the requested number of songs will result in a failed power hour.`
  }
]