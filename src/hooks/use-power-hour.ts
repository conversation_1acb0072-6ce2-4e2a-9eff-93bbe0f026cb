"use client";

import { firestore } from "@/app/firebase/firebase";
import { PowerHour, PowerHourID } from "@/models/power-hour";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useDocument } from "react-firebase-hooks/firestore";
import { doc } from "firebase/firestore";
import { toast } from "react-toastify";
import { COLLECTIONS } from "@/app/firebase/firebase";

export const usePowerHour = (powerHourId?: PowerHourID) => {
  // Always call useDocument with a valid reference, even if not ready
  const docRef =
    typeof firestore === "object" &&
    firestore !== null &&
    typeof firestore.app !== "undefined" &&
    powerHourId
      ? doc(firestore, COLLECTIONS.POWER_HOURS, powerHourId)
      : typeof firestore === "object" && firestore !== null
      ? doc(firestore, COLLECTIONS.POWER_HOURS, "placeholder-doc-id")
      : null;

  const [snapshot, loading, hookError] = useDocument(docRef);

  // Determine if we are actually ready to show data
  const isValid =
    typeof firestore === "object" &&
    firestore !== null &&
    typeof firestore.app !== "undefined" &&
    !!powerHourId;

  let powerHour: PowerHour | null = null;
  if (
    isValid &&
    snapshot &&
    typeof snapshot.exists === "function" &&
    snapshot.exists()
  ) {
    const data = snapshot.data();
    if (data && typeof data === "object") {
      powerHour = { id: snapshot.id, ...data } as PowerHour;
    }
  }

  // Error handling
  let error: Error | null = null;
  if (!isValid) {
    error = new Error("Firebase not available or powerHourId missing");
  } else if (hookError instanceof Error) {
    error = hookError;
    toast.error("Error fetching power hour. Please try again.");
  }

  return {
    powerHour,
    loading: !isValid || loading,
    error,
  };
};
