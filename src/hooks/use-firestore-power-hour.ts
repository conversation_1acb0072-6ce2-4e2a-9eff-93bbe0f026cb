"use client";

import { useEffect, useState } from "react";
import {
  doc,
  collection,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { useDocument, useCollection } from "react-firebase-hooks/firestore";
import {
  firestore,
  isFirestoreAvailable,
  onFirebaseReady,
  COLLECTIONS,
} from "../app/firebase/firebase";
import {
  PowerHour,
  PowerHourID,
  PowerHourGenerationSteps,
} from "../models/power-hour";

/**
 * Hook to fetch and watch a power hour document from Firestore
 * @param powerHourId The ID of the power hour to fetch
 * @returns Object containing the power hour data, loading state, and error
 */
export const useFirestorePowerHour = (powerHourId: PowerHourID) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);
  const [customError, setCustomError] = useState<Error | null>(null);

  // Always call useDocument with a valid reference, even if we're not ready yet
  // We'll create a ref that points to a non-existent document when not ready
  const docRef =
    isReady && isFirestoreAvailable() && powerHourId && firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, powerHourId)
      : firestore
      ? doc(firestore, COLLECTIONS.POWER_HOURS, "placeholder-doc-id")
      : null;

  // Always call the hook, never conditionally
  const [snapshot, loading, firestoreError] = useDocument(docRef, {
    snapshotListenOptions: { includeMetadataChanges: false }
  });

  // Initialize Firebase on component mount
  useEffect(() => {
    // Clear any previous errors
    setCustomError(null);

    // Set custom error if no powerHourId is provided
    if (!powerHourId) {
      setCustomError(new Error("No power hour ID provided"));
      return;
    }

    // Initialize Firebase
    onFirebaseReady(() => {
      setIsReady(true);
    });
  }, [powerHourId]);

  // Combine all error states
  const error = customError || firestoreError;

  // Determine actual loading state
  const isLoading =
    loading || !isReady || !isFirestoreAvailable() || !powerHourId;

  // Process the snapshot only when we're ready and have valid data
  const powerHour =
    !isLoading && snapshot?.exists()
      ? ({ id: snapshot.id, ...snapshot.data() } as PowerHour)
      : null;

  return { powerHour, loading: isLoading, error };
};

/**
 * Hook to check if a power hour is complete
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing completion status, loading state, and error
 */
export const useIsPowerHourComplete = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour is complete
  const isComplete =
    powerHour?.currentStep === PowerHourGenerationSteps.Complete;

  return { isComplete, loading, error };
};

/**
 * Hook to check if a power hour has any errors
 * @param powerHourId The ID of the power hour to check
 * @returns Object containing error status, loading state, and error
 */
export const useHasPowerHourErrors = (powerHourId: PowerHourID) => {
  const { powerHour, loading, error } = useFirestorePowerHour(powerHourId);

  // Check if the power hour has errors
  const hasErrors = Boolean(powerHour?.error);

  // The error can be either a string or an object with a message property
  let errorMessage: string | null = null;
  if (powerHour?.error) {
    if (typeof powerHour.error === "string") {
      errorMessage = powerHour.error;
    } else if (
      typeof powerHour.error === "object" &&
      powerHour.error !== null
    ) {
      // Try to get message from error object if it exists
      errorMessage = (powerHour.error as any).message || "Unknown error";
    }
  }

  return { hasErrors, errorMessage, loading, error };
};

/**
 * Hook to fetch multiple power hours from Firestore
 * @param limit Maximum number of power hours to fetch
 * @returns Object containing the power hours array, loading state, and error
 */
export const useFirestorePowerHours = (limitCount?: number) => {
  // Always define state hooks at the top level
  const [isReady, setIsReady] = useState(false);

  // Create a query that's valid even if we're not ready yet
  let powerHoursQuery;
  if (isReady && isFirestoreAvailable() && firestore) {
    // Valid query when ready
    if (typeof limitCount === "number") {
      powerHoursQuery = query(
        collection(firestore, COLLECTIONS.POWER_HOURS),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      );
    } else {
      powerHoursQuery = query(
        collection(firestore, COLLECTIONS.POWER_HOURS),
        orderBy("createdAt", "desc")
      );
    }
  } else if (firestore) {
    // Fallback query for when we're not ready
    // This ensures the hook is always called with a valid reference
    powerHoursQuery = query(collection(firestore, COLLECTIONS.POWER_HOURS));
  } else {
    powerHoursQuery = null;
  }

  // Initialize on component mount - always call useEffect
  useEffect(() => {
    onFirebaseReady(() => {
      setIsReady(true);
    });
  }, []);

  // Always call the hook, never conditionally
  const [snapshot, loading, error] = useCollection(powerHoursQuery);

  // Determine actual loading state
  const isLoading = loading || !isReady || !isFirestoreAvailable();

  // Only process snapshot data when we're actually ready
  let powerHours: PowerHour[] = [];
  if (!isLoading && snapshot) {
    powerHours = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as PowerHour[];
  }

  return { powerHours, loading: isLoading, error };
};
