// Mock for Firebase and Firestore

export const mockDoc = jest.fn();
export const mockCollection = jest.fn();
export const mockGetDoc = jest.fn();
export const mockGetDocs = jest.fn();
export const mockSetDoc = jest.fn();
export const mockUpdateDoc = jest.fn();
export const mockAddDoc = jest.fn();
export const mockQuery = jest.fn();
export const mockWhere = jest.fn();
export const mockOrderBy = jest.fn();
export const mockLimit = jest.fn();
export const mockOnSnapshot = jest.fn();

// Mock return values
export const mockDocSnap = {
  exists: jest.fn().mockReturnValue(true),
  data: jest.fn().mockReturnValue({ id: 'mock-doc-id', title: 'Mock Power Hour' }),
  id: 'mock-doc-id',
};

export const mockQuerySnap = {
  empty: false,
  docs: [
    {
      id: 'mock-doc-id-1',
      data: () => ({ title: 'Mock Power Hour 1' }),
      exists: () => true,
    },
    {
      id: 'mock-doc-id-2',
      data: () => ({ title: 'Mock Power Hour 2' }),
      exists: () => true,
    },
  ],
  forEach: jest.fn(),
};

// Mock firestore
export const mockFirestore = {
  app: {},
};

// Mock the actual Firebase modules
jest.mock('firebase/firestore', () => ({
  doc: (...args: any[]) => {
    mockDoc(...args);
    return { id: 'mock-doc-id' };
  },
  collection: (...args: any[]) => {
    mockCollection(...args);
    return { id: 'mock-collection-id' };
  },
  getDoc: (...args: any[]) => {
    mockGetDoc(...args);
    return Promise.resolve(mockDocSnap);
  },
  getDocs: (...args: any[]) => {
    mockGetDocs(...args);
    return Promise.resolve(mockQuerySnap);
  },
  setDoc: (...args: any[]) => {
    mockSetDoc(...args);
    return Promise.resolve();
  },
  updateDoc: (...args: any[]) => {
    mockUpdateDoc(...args);
    return Promise.resolve();
  },
  addDoc: (...args: any[]) => {
    mockAddDoc(...args);
    return Promise.resolve({ id: 'new-doc-id' });
  },
  query: (...args: any[]) => {
    mockQuery(...args);
    return 'mock-query';
  },
  where: (...args: any[]) => {
    mockWhere(...args);
    return 'mock-where-constraint';
  },
  orderBy: (...args: any[]) => {
    mockOrderBy(...args);
    return 'mock-orderBy-constraint';
  },
  limit: (...args: any[]) => {
    mockLimit(...args);
    return 'mock-limit-constraint';
  },
  onSnapshot: (...args: any[]) => {
    mockOnSnapshot(...args);
    // Return an unsubscribe function
    return jest.fn();
  },
}));

jest.mock('../app/firebase/firebase', () => ({
  COLLECTIONS: {
    POWER_HOURS: 'power-hours',
    FEEDBACK: 'feedback',
  },
  firestore: mockFirestore,
  isFirebaseAvailable: jest.fn().mockReturnValue(true),
}));
