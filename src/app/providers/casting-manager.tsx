"use client";

import { createContext, useContext, useState, useEffect, useCallback } from "react";
import { toast } from "react-toastify";

// Add Chrome Cast API type definitions
declare global {
  interface Window {
    chrome?: {
      cast?: {
        // Basic casting framework
        isAvailable?: boolean;
        initialize: (apiConfig: any, onInitSuccess: () => void, onError?: (error: Error) => void) => void;
        requestSession: (onSuccess: (session: any) => void, onError: (error: Error) => void) => void;

        // Receiver API
        ReceiverAvailability: {
          AVAILABLE: string;
          UNAVAILABLE: string;
        };

        // Media API
        media: {
          // Media types and objects
          MediaInfo: new (contentId: string, contentType: string) => any;
          GenericMediaMetadata: new () => any;
          LoadRequest: new (mediaInfo: any) => any;
        };

        // Session management
        SessionRequest: new (appId: string) => any;
        ApiConfig: new (sessionRequest: any, sessionListener: (session: any) => void, receiverListener: (availability: string) => void, autoJoinPolicy?: string) => any;
        AutoJoinPolicy: {
          ORIGIN_SCOPED: string;
          TAB_AND_ORIGIN_SCOPED: string;
          PAGE_SCOPED: string;
        };
      };
    };
    __onGCastApiAvailable?: ((isAvailable: boolean) => void) | undefined;
  }
}

interface CastingDevice {
  id: string;
  name: string;
}

interface MediaOptions {
  title: string;
  url: string;
  mimeType: string;
  startTime?: number;
  endTime?: number;
}

interface CastingContextType {
  isAvailable: boolean;
  isConnected: boolean;
  isCasting: boolean;
  castingMode: 'chromecast' | 'tab' | 'none';
  availableDevices: CastingDevice[];
  selectedDevice: CastingDevice | null;
  connectToDevice: (deviceId: string) => Promise<boolean>;
  disconnectFromDevice: () => void;
  castMedia: (options: MediaOptions) => void;
  castTab: () => void;
  castDesktop: () => void;
  stopCasting: () => void;
}

const CastingContext = createContext<CastingContextType>({
  isAvailable: false,
  isConnected: false,
  isCasting: false,
  castingMode: 'none',
  availableDevices: [],
  selectedDevice: null,
  connectToDevice: async () => false,
  disconnectFromDevice: () => {},
  castMedia: () => {},
  castTab: () => {},
  castDesktop: () => {},
  stopCasting: () => {},
});

export const useCasting = () => useContext(CastingContext);

export const CastingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAvailable, setIsAvailable] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isCasting, setIsCasting] = useState(false);
  const [castingMode, setCastingMode] = useState<'chromecast' | 'tab' | 'none'>('none');
  const [availableDevices, setAvailableDevices] = useState<CastingDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<CastingDevice | null>(null);
  const [castingSession, setCastingSession] = useState<any>(null);

  // Session listener function
  const sessionListener = useCallback((session: any) => {
    console.log('New session', session);
    setCastingSession(session);
    setIsConnected(true);
    
    // If the session already has an active media session, update casting state
    if (session.media && session.media.length > 0) {
      setIsCasting(true);
    }
  }, []);

  // Receiver listener function
  const receiverListener = useCallback((availability: string) => {
    if (window.chrome?.cast?.ReceiverAvailability) {
      if (availability === window.chrome.cast.ReceiverAvailability.AVAILABLE) {
        console.log('Receivers are available');
        setIsAvailable(true);
        scanForDevices();
      } else {
        console.log('No receivers are available');
        setIsAvailable(false);
        setAvailableDevices([]);
      }
    }
  }, []);

  // Scan for available Cast devices
  const scanForDevices = useCallback(() => {
    // This function is placeholder as the actual device discovery happens through the Chrome Cast API
    // When a device is selected through the Cast dialog, it will be handled by the session listener
    console.log('Scanning for devices...');
    // In a real implementation, you would use the Cast SDK to discover devices
  }, []);

  // Initialize the Cast API
  const initializeCastApi = useCallback(() => {
    // Check if chrome.cast is available
    if (typeof window !== 'undefined' && window.chrome && window.chrome.cast) {
      try {
        // Initialize casting
        const sessionRequest = new window.chrome.cast.SessionRequest(
          // Chrome receiver app ID (for YouTube)
          '4F8B3483' // Default media receiver app ID
        );

        const apiConfig = new window.chrome.cast.ApiConfig(
          sessionRequest,
          sessionListener,
          receiverListener,
          window.chrome.cast.AutoJoinPolicy?.ORIGIN_SCOPED || 'origin_scoped'
        );

        window.chrome.cast.initialize(
          apiConfig, 
          () => {
            console.log('Cast API initialized');
            setIsAvailable(true);
            scanForDevices();
          }, 
          (error: Error) => {
            console.error('Failed to initialize Cast API:', error);
            setIsAvailable(false);
          }
        );
      } catch (err) {
        console.error('Error during Cast API initialization:', err);
        setIsAvailable(false);
      }
    } else {
      console.log('Chrome cast not available');
      setIsAvailable(false);
    }
  }, [sessionListener, receiverListener, scanForDevices]);

  // Initialize Google Cast API
  useEffect(() => {
    // Check if chrome.cast is available
    if (typeof window !== 'undefined' && window.chrome && window.chrome.cast) {
      initializeCastApi();
    } else {
      // Load the Cast API
      const script = document.createElement('script');
      script.src = 'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';
      script.async = true;
      document.body.appendChild(script);
      
      // Wait for the API to be available
      window.__onGCastApiAvailable = (isAvailable: boolean) => {
        if (isAvailable) {
          initializeCastApi();
        } else {
          console.error('Cast API is not available');
          setIsAvailable(false);
        }
      };
    }

    return () => {
      // Clean up by removing the global callback
      if (window.__onGCastApiAvailable) {
        delete window.__onGCastApiAvailable;
      }
    };
  }, []);

  // Connect to a specific casting device
  const connectToDevice = useCallback(async (deviceId: string) => {
    const device = availableDevices.find(d => d.id === deviceId);
    
    if (!device) {
      toast.error('Device not found');
      return false;
    }
    
    setSelectedDevice(device);
    
    return new Promise<boolean>((resolve) => {
      if (window.chrome?.cast?.requestSession) {
        try {
          window.chrome.cast.requestSession(
            (session: any) => {
              setCastingSession(session);
              setIsConnected(true);
              toast.success(`Connected to ${device.name}`);
              resolve(true);
            },
            (error: Error) => {
              console.error('Error connecting to device:', error);
              toast.error('Failed to connect to device');
              resolve(false);
            }
          );
        } catch (err) {
          console.error('Error requesting cast session:', err);
          toast.error('Error initiating casting session');
          resolve(false);
        }
      } else {
        // Fallback for testing or when cast API is not available
        setTimeout(() => {
          setIsConnected(true);
          toast.success(`Connected to ${device.name}`);
          resolve(true);
        }, 1000);
      }
    });
  }, [availableDevices]);

  // Disconnect from the current device
  const disconnectFromDevice = useCallback(() => {
    if (castingSession) {
      try {
        castingSession.leave(
          () => {
            setIsConnected(false);
            setIsCasting(false);
            setCastingMode('none');
            setSelectedDevice(null);
            setCastingSession(null);
            toast.info('Disconnected from device');
          },
          (error: Error) => {
            console.error('Error disconnecting:', error);
            toast.error('Error disconnecting from device');
          }
        );
      } catch (err) {
        console.error('Error during disconnect:', err);
        // Clean up state even if the API call fails
        setIsConnected(false);
        setIsCasting(false);
        setCastingMode('none');
        setSelectedDevice(null);
        setCastingSession(null);
      }
    } else {
      setIsConnected(false);
      setIsCasting(false);
      setCastingMode('none');
      setSelectedDevice(null);
      setCastingSession(null);
    }
  }, [castingSession]);

  // Cast media to the current device
  const castMedia = useCallback((options: MediaOptions) => {
    if (!castingSession || !window.chrome?.cast?.media) {
      toast.error('No active casting session');
      return;
    }

    try {
      const mediaInfo = new window.chrome.cast.media.MediaInfo(
        options.url,
        options.mimeType || 'video/mp4'
      );

      // Set video metadata
      const metadata = new window.chrome.cast.media.GenericMediaMetadata();
      metadata.title = options.title || 'Power Hour Media';
      mediaInfo.metadata = metadata;

      // Set custom properties for start and end time if provided
      mediaInfo.customData = {
        startTime: options.startTime || 0,
        endTime: options.endTime || undefined,
      };

      const request = new window.chrome.cast.media.LoadRequest(mediaInfo);
      if (options.startTime) {
        request.currentTime = options.startTime;
      }
      
      castingSession.loadMedia(request, 
        (media: any) => {
          console.log('Media loaded successfully', media);
          setIsCasting(true);
          setCastingMode('chromecast');
          toast.success('Now casting to device');
        },
        (error: Error) => {
          console.error('Error loading media:', error);
          toast.error('Failed to cast media');
        }
      );
    } catch (error) {
      console.error('Error casting media:', error);
      toast.error('Error casting media');
    }
  }, [castingSession]);

  // Cast the current tab
  const castTab = useCallback(() => {
    if (!window.chrome || !window.chrome.cast || !window.chrome.cast.requestSession) {
      toast.error('Chrome casting API not available');
      return;
    }
    
    try {
      // When using tab casting, we use a different approach
      // This opens the casting dialog and lets users select a device
      window.chrome.cast.requestSession(
        (session: any) => {
          setCastingSession(session);
          setIsConnected(true);
          setIsCasting(true);
          setCastingMode('tab');
          
          // Get the device info from the session
          if (session.receiver) {
            setSelectedDevice({
              id: session.receiver.id,
              name: session.receiver.friendlyName
            });
          }
          
          toast.success('Now casting current tab');
        },
        (error: Error) => {
          console.error('Error casting tab:', error);
          toast.error('Failed to cast tab');
        }
      );
    } catch (error) {
      console.error('Error initiating tab casting:', error);
      toast.error('Error initiating tab casting');
    }
  }, []);
  
  // Cast the entire desktop/window
  const castDesktop = useCallback(() => {
    if (!window.chrome || !window.chrome.cast || !window.chrome.cast.requestSession) {
      toast.error('Chrome casting API not available');
      return;
    }
    
    try {
      // Note: Desktop casting requires special permissions and might not work in all browsers
      // This is similar to tab casting but with a specific intent for desktop casting
      window.chrome.cast.requestSession(
        (session: any) => {
          setCastingSession(session);
          setIsConnected(true);
          setIsCasting(true);
          setCastingMode('tab'); // We use 'tab' mode as desktop is handled similarly
          
          // Get the device info from the session
          if (session.receiver) {
            setSelectedDevice({
              id: session.receiver.id,
              name: session.receiver.friendlyName
            });
          }
          
          toast.success('Now casting desktop');
        },
        (error: Error) => {
          console.error('Error casting desktop:', error);
          toast.error('Failed to cast desktop');
        }
      );
    } catch (error) {
      console.error('Error initiating desktop casting:', error);
      toast.error('Error initiating desktop casting');
    }
  }, []);

  const stopCasting = useCallback(() => {
    if (castingSession) {
      try {
        // For media casting
        if (castingMode === 'chromecast' && castingSession.media && castingSession.media.length > 0) {
          castingSession.media[0].stop(null, 
            () => {
              setIsCasting(false);
              setCastingMode('none');
              toast.info('Stopped casting media');
            }, 
            (error: Error) => {
              console.error('Error stopping cast:', error);
              toast.error('Error stopping cast');
            }
          );
        } 
        // For tab or desktop casting
        else if (castingMode === 'tab') {
          castingSession.stop(null,
            () => {
              setIsCasting(false);
              setCastingMode('none');
              toast.info('Stopped casting tab/desktop');
            },
            (error: Error) => {
              console.error('Error stopping tab/desktop cast:', error);
              toast.error('Error stopping cast');
            }
          );
        }
      } catch (err) {
        console.error('Error while stopping casting:', err);
        // Reset state even if the API call fails
        setIsCasting(false);
        setCastingMode('none');
        toast.error('Error while stopping cast, but state has been reset');
      }
    }
  }, [castingSession, castingMode]);

  const value = {
    isAvailable,
    isConnected,
    isCasting,
    castingMode,
    availableDevices,
    selectedDevice,
    connectToDevice,
    disconnectFromDevice,
    castMedia,
    castTab,
    castDesktop,
    stopCasting,
  };

  return (
    <CastingContext.Provider value={value}>
      {children}
    </CastingContext.Provider>
  );
};



export default CastingContext;
