"use client";

import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  Unsubscribe,
  DocumentData,
  DocumentSnapshot,
  QuerySnapshot,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import {
  firestore,
  COLLECTIONS,
  isFirebaseAvailable,
} from "../firebase/firebase";
import {
  PowerHour,
  PowerHourID,
  PowerHourGenerationSteps,
} from "../../models/power-hour";
// @ts-ignore - Ignore missing type declarations
import { toast } from "react-toastify";

// Helper to check if Firestore is initialized and available
const isFirestoreAvailable = (): boolean => {
  if (!isFirebaseAvailable() || !firestore) {
    console.warn("Firestore is not available");
    return false;
  }
  return true;
};

/**
 * Fetches a single power hour by its unique identifier.
 * @param {PowerHourID} powerHourId The unique identifier for the power hour.
 * @returns {Promise<PowerHour | null>} A promise that resolves to the fetched PowerHour object, or null if not found.
 */
export const fetchPowerHour = async (
  powerHourId: PowerHourID
): Promise<PowerHour | null> => {
  if (!powerHourId) {
    console.log("Invalid powerHourId provided");
    return null;
  }

  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for fetching power hour");
    toast.error("Unable to connect to the database");
    return null;
  }

  try {
    console.log("Fetching power hour from Firestore with ID:", powerHourId);
    const docRef = doc(firestore!, COLLECTIONS.POWER_HOURS, powerHourId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const powerHourData = docSnap.data() as PowerHour;
      console.log("Found power hour in Firestore:", powerHourData.title);
      return { ...powerHourData, id: powerHourId };
    } else {
      console.log("Power hour not found in Firestore");
      return null;
    }
  } catch (error) {
    console.error("Error fetching power hour:", error);
    toast.error(
      "Error loading power hour: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return null;
  }
};

/**
 * Creates a new power hour entry in the database.
 * @param {PowerHour} powerHour The PowerHour object to create.
 * @returns {Promise<string | null>} A promise that resolves to the new power hour ID, or null if creation failed.
 */
export const createPowerHour = async (
  powerHour: PowerHour
): Promise<string | null> => {
  if (!powerHour) {
    console.error("Invalid power hour object provided to createPowerHour");
    toast.error("Invalid power hour data");
    return null;
  }

  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for creating power hour");
    toast.error("Unable to connect to the database");
    return null;
  }

  try {
    console.log("Creating new power hour:", powerHour.title);

    // Prepare power hour with metadata
    const enhancedPowerHour = {
      ...powerHour,
      upvotes: 0,
      downvotes: 0,
      feedback: { reportedIssues: [] },
      currentStep: PowerHourGenerationSteps.None,
      stepProgress: 0,
      createdAt: Date.now(),
      lastUpdateTime: Date.now(),
    };

    let documentId: string;

    // If ID is provided, use it, otherwise let Firestore generate one
    if (powerHour.id) {
      documentId = powerHour.id;
      await setDoc(
        doc(firestore!, COLLECTIONS.POWER_HOURS, documentId),
        enhancedPowerHour
      );
    } else {
      // Use Firestore's auto-generated ID
      const docRef = await addDoc(
        collection(firestore!, COLLECTIONS.POWER_HOURS),
        enhancedPowerHour
      );
      documentId = docRef.id;
    }

    console.log("Power hour created successfully with ID:", documentId);
    toast.success("Power hour created successfully");
    return documentId;
  } catch (error: any) {
    console.error("Error creating power hour:", error);
    toast.error(
      "Failed to create power hour: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return null;
  }
};

/**
 * Updates an existing power hour entry with partial or full power hour data.
 * @param {Partial<PowerHour>} powerHour A subset of PowerHour properties to update.
 * @returns {Promise<string | null>} A promise that resolves to the power hour ID, or null if update failed.
 */
export const updatePowerHour = async (
  powerHour: Partial<PowerHour>
): Promise<string | null> => {
  if (!powerHour?.id) {
    console.error("PowerHour ID is required for updatePowerHour");
    toast.error("Power hour ID is required for updates");
    return null;
  }

  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for updating power hour");
    toast.error("Unable to connect to the database");
    return null;
  }

  try {
    console.log("Updating power hour with ID:", powerHour.id);

    // Add timestamp to track when the update occurred
    const updateData = {
      ...powerHour,
      lastUpdateTime: Date.now(),
    };

    // Exclude the ID from the update data as it's used for the document reference
    const { id, ...dataToUpdate } = updateData;

    // Update the document
    const docRef = doc(firestore!, COLLECTIONS.POWER_HOURS, powerHour.id!);
    await updateDoc(docRef, dataToUpdate);

    console.log("Power hour updated successfully");
    return powerHour.id!;
  } catch (error: any) {
    console.error("Error updating power hour:", error);
    toast.error(
      "Failed to update power hour: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    // Return the ID even on error for reference
    return powerHour.id ?? null;
  }
};

/**
 * Sets (replaces) an existing power hour entry with new data.
 * @param {Partial<PowerHour>} powerHour The new PowerHour data to set.
 * @returns {Promise<string | null>} A promise that resolves to the power hour ID, or null if the operation failed.
 */
export const setPowerHour = async (
  powerHour: Partial<PowerHour>
): Promise<string | null> => {
  if (!powerHour?.id) {
    const errorMessage = "PowerHour ID is required for setPowerHour";
    console.error(errorMessage);
    toast.error(errorMessage);
    return null;
  }

  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for setting power hour");
    toast.error("Unable to connect to the database");
    return null;
  }

  try {
    // After the early return, powerHour.id is guaranteed to be defined
    // Replace the entire document with the new data
    const docRef = doc(firestore!, COLLECTIONS.POWER_HOURS, powerHour.id!);

    // Add last update timestamp
    const dataToSet = {
      ...powerHour,
      lastUpdateTime: Date.now(),
    };

    await setDoc(docRef, dataToSet);
    console.log("Power hour set successfully");
    return powerHour.id!;
  } catch (error) {
    console.error("Error setting power hour:", error);
    toast.error(
      "Error setting power hour: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return null;
  }
};

/**
 * Saves a power hour entry, ensuring unique title names by appending a counter to duplicates.
 * @param {PowerHour} powerHour The PowerHour object to save.
 * @returns {Promise<string>} A promise that resolves to the final name used for the power hour.
 */
export const savePowerHour = async (powerHour: PowerHour): Promise<string> => {
  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for saving power hour");
    toast.error("Unable to connect to the database");
    return powerHour.title; // Return original title as fallback
  }

  try {
    let listName = powerHour.title;
    let counter = 1;
    let isUnique = false;

    while (!isUnique) {
      // Check if a power hour with this title already exists
      const q = query(
        collection(firestore!, COLLECTIONS.POWER_HOURS),
        where("title", "==", listName),
        limit(1)
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        // Title is unique, we can use it
        isUnique = true;
      } else {
        // Title already exists, append counter and try again
        listName = `${powerHour.title} (${counter})`;
        counter++;
      }
    }

    // Now save with the unique name
    const updatedPowerHour = {
      ...powerHour,
      title: listName,
      createdAt: Date.now(),
      lastUpdateTime: Date.now(),
    };

    // Use the createPowerHour function to save it
    const result = await createPowerHour(updatedPowerHour);
    if (result) {
      console.log("Power hour saved with unique name:", listName);
      return listName;
    } else {
      throw new Error("Failed to save power hour");
    }
  } catch (error) {
    console.error("Error saving power hour with unique name:", error);
    toast.error(
      "Error saving power hour: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return powerHour.title; // Return original title as fallback
  }
};

/**
 * Sets up a real-time watcher on a specific power hour, triggering a callback on data change.
 * @param {PowerHourID} powerHourId The unique identifier for the power hour to watch.
 * @param {(powerHour: PowerHour | null) => void} onChange A callback function to run whenever the watched data changes.
 * @returns {Unsubscribe} A function that, when called, will unsubscribe from the updates.
 */
export const watchPowerHour = (
  powerHourId: PowerHourID,
  onChange: (powerHour: PowerHour | null) => void
): Unsubscribe => {
  if (!isFirestoreAvailable() || !powerHourId) {
    console.error("Firestore is not available or invalid power hour ID");
    // Return a no-op unsubscribe function
    return () => {};
  }

  console.log("Setting up Firestore listener for power hour ID:", powerHourId);
  const docRef = doc(firestore!, COLLECTIONS.POWER_HOURS, powerHourId);

  return onSnapshot(
    docRef,
    (docSnapshot: DocumentSnapshot<DocumentData>) => {
      if (docSnapshot.exists()) {
        const data = docSnapshot.data() as PowerHour;
        onChange({ ...data, id: powerHourId });
      } else {
        console.warn(
          `Power hour with ID ${powerHourId} not found or was deleted`
        );
        onChange(null);
      }
    },
    (error: any) => {
      console.error("Error watching power hour:", error);
      toast.error("Error watching for power hour updates");
      onChange(null);
    }
  );
};

/**
 * Fetches all power hours from the database.
 * @returns {Promise<PowerHour[]>} A promise that resolves to an array of all PowerHour objects.
 */
export const fetchAllPowerHours = async (): Promise<PowerHour[]> => {
  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for fetching power hours");
    toast.error("Unable to connect to the database");
    return [];
  }

  try {
    const querySnapshot = await getDocs(
      collection(firestore!, COLLECTIONS.POWER_HOURS)
    );
    const powerHours: PowerHour[] = [];

    querySnapshot.forEach(
      (docSnapshot: QueryDocumentSnapshot<DocumentData>) => {
        const data = docSnapshot.data() as PowerHour;
        powerHours.push({ ...data, id: docSnapshot.id });
      }
    );

    return powerHours;
  } catch (error: any) {
    console.error("Error fetching all power hours:", error);
    toast.error(
      "Error fetching power hours: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return [];
  }
};

/**
 * Fetches the top power hours sorted by upvotes, limited by a specified number.
 * @param {number} limit The maximum number of power hours to fetch.
 * @returns {Promise<PowerHour[]>} A promise that resolves to an array of the top PowerHour objects.
 */
export const fetchTopPowerHours = async (
  limitCount: number
): Promise<PowerHour[]> => {
  if (!isFirestoreAvailable()) {
    console.error("Firestore is not available for fetching top power hours");
    toast.error("Unable to connect to the database");
    return [];
  }

  try {
    // Create a query sorted by upvotes in descending order (most upvotes first)
    const q = query(
      collection(firestore!, COLLECTIONS.POWER_HOURS),
      orderBy("upvotes", "desc"),
      limit(limitCount)
    );

    const querySnapshot = await getDocs(q);
    const powerHours: PowerHour[] = [];

    querySnapshot.forEach(
      (docSnapshot: QueryDocumentSnapshot<DocumentData>) => {
        const data = docSnapshot.data() as PowerHour;
        powerHours.push({ ...data, id: docSnapshot.id });
      }
    );

    return powerHours;
  } catch (error: any) {
    console.error("Error fetching top power hours:", error);
    toast.error(
      "Error fetching top power hours: " +
        (error instanceof Error ? error.message : "Unknown error")
    );
    return [];
  }
};
