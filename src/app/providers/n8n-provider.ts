import { toast } from "react-toastify";

/**
 * Interface for error responses from n8n
 */
interface ErrorResponse {
  success: boolean;
  error: string;
  details?: any;
  errorCode?: string;
  timestamp: string;
  message?: string;
}

/**
 * Parse error response from n8n
 */
const parseErrorResponse = (error: any): ErrorResponse => {
  // Try to extract the error response from the error object
  if (error.response?.data) {
    return error.response.data;
  }

  // Fallback for network errors or other issues
  return {
    success: false,
    error: error.message || "Unknown error occurred",
    errorCode: "NETWORK_ERROR",
    timestamp: new Date().toISOString(),
  };
};

/**
 * Helper function to handle errors from n8n
 */
const handleN8nError = (error: any): ErrorResponse => {
  const errorResponse = parseErrorResponse(error);

  // Display toast notification with error details
  toast.error(`Error: ${errorResponse.error}`);

  console.error("n8n error:", errorResponse);

  return errorResponse;
};

/**
 * n8n API with error handling
 */
export const N8nProvider = {
  /**
   * Create a power hour based on search term
   * @param {string} search The search term to create power hour from
   * @returns {Promise<{powerHourId: string} | {error: ErrorResponse}>} A promise that resolves to the power hour ID or an error
   */
  createPowerHour: async (search: string) => {
    console.log("Creating power hour with search:", search);
    try {
      const endpoint = "https://n8n-pve.ensoq.ddns.net/webhook/createPowerHour";

      console.log("Making POST request to n8n endpoint with search:", search);
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          search,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw { response: { data: errorData }, status: response.status };
      }

      const data = await response.json();
      console.log("Response from n8n:", data);

      if (data && data.powerHourId) {
        toast.success("Power hour creation started successfully");
        return data; // Return the data containing powerHourId
      } else {
        console.error("No powerHourId in response:", data);
        toast.warning("Power hour was created but ID was not returned");
        // Return a format compatible with what the component expects
        return { powerHourId: null };
      }
    } catch (error) {
      const errorResponse = handleN8nError(error);
      console.error("Error in createPowerHour:", errorResponse);

      // Return a structured error that can be handled by the UI
      return { error: errorResponse };
    }
  },
};
