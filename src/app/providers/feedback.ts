
import { firestore } from "../firebase/firebase";
import { doc, setDoc, updateDoc, increment, runTransaction, getDoc, collection, getDocs } from "firebase/firestore";
import { PHEntryFeedback, PHFeedback, PHEFeedbackOpts, PowerHourFeedbackOptions } from "../../models/feedback";
import { toast } from "react-toastify";

export const fetchFeedbackPowerHour = async (powerHourId: string): Promise<PHFeedback> => {
  if (!firestore) {
    console.error("Firestore not initialized");
    return {} as PHFeedback;
  }
  const docRef = doc(firestore, "power-hour-feedback", powerHourId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    console.log("Document data:", docSnap.data());
    return docSnap.data() as PHFeedback;
  } else {
    // doc.data() will be undefined in this case
    // console.log("No such document!");
  }
  return {} as PHFeedback;
}

export const fetchFeedbackPowerHourEntry = async (powerHourId: string, powerHourEntryId: string) => {
  if (!firestore) {
    console.error("Firestore not initialized");
    return {} as PHEntryFeedback;
  }
  const docRef = doc(firestore, "power-hour-feedback", powerHourId, "entries", powerHourEntryId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    console.log("Document data:", docSnap.data());
    return docSnap.data() as PHEntryFeedback;
  } else {
    // doc.data() will be undefined in this case
    // console.log("No such document!");
  }
  return {} as PHEntryFeedback;
}

export const fetchFeedbackPowerHourEntryList = async (powerHourId: string): Promise<PHEntryFeedback[]> => {
  if (!powerHourId) return [] as PHEntryFeedback[];
  if (!firestore) {
    console.error("Firestore not initialized");
    return [] as PHEntryFeedback[];
  }
  const collectionRef = collection(firestore, "power-hour-feedback", powerHourId, "entries");
  const snapshot = await getDocs(collectionRef);
  if (!snapshot.empty) {
    return snapshot.docs.map(doc => doc.data()) as PHEntryFeedback[];
  } else {
    // doc.data() will be undefined in this case
    // console.warn("No such document!");
  }
  return [] as PHEntryFeedback[];
}


/**
 * Function to submit feedback for a Power Hour.
 * @param {string} powerHourId - The ID of the Power Hour.
 * @param {PHFeedback} feedbackData - The feedback data to submit.
 */
export const submitFeedbackPowerHour = async (feedbackData: PHFeedback) => {
  if (!feedbackData || !feedbackData.powerHourId) {
    toast.error("Error submitting feedback, missing PHID");
    throw new Error("Error submitting feedback, missing PHID");
  }

  if (!firestore) {
    toast.error("Firestore not initialized");
    throw new Error("Firestore not initialized");
  }

  const fdbk = feedbackData.feedback;
  try {
    const docRef = doc(firestore, "power-hour-feedback", feedbackData.powerHourId);
    runTransaction(firestore, async (transaction) => {
      const document = await transaction.get(docRef);

      if (!document.exists()) {
        transaction.set(docRef, feedbackData, { merge: true });
      } else {
        await transaction.update(docRef, {
          ["upvotes"]: increment(feedbackData.upvotes ?? 0),
          ["downvotes"]: increment(feedbackData.downvotes ?? 0),
          [`feedback.${PowerHourFeedbackOptions.ProblemWithPlayback}`]: increment(fdbk && fdbk[PowerHourFeedbackOptions.ProblemWithPlayback] ? fdbk[PowerHourFeedbackOptions.ProblemWithPlayback] : 0),
          [`feedback.${PowerHourFeedbackOptions.ProblemWithSongs}`]: increment(fdbk && fdbk![PowerHourFeedbackOptions.ProblemWithSongs] ? fdbk![PowerHourFeedbackOptions.ProblemWithSongs] : 0),
          [`feedback.${PowerHourFeedbackOptions.ProblemWithVideos}`]: increment(fdbk && fdbk![PowerHourFeedbackOptions.ProblemWithVideos] ? fdbk![PowerHourFeedbackOptions.ProblemWithVideos] : 0),
          [`feedback.${PowerHourFeedbackOptions.ProblemWithPowerHour}`]: increment(fdbk && fdbk![PowerHourFeedbackOptions.ProblemWithPowerHour] ? fdbk![PowerHourFeedbackOptions.ProblemWithPowerHour] : 0),
        });
      }
    });
    console.log("Feedback submitted successfully");
  } catch (error) {
    console.error("Error submitting feedback:", error);
    throw new Error("Error submitting feedback: " + error);
  }



};

export const submitFeedbackPowerHourEntry = async (feedbackData: Partial<PHEntryFeedback>) => {
  if (!feedbackData || !feedbackData.powerHourId || !feedbackData.powerHourEntryId) {
    toast.error("Error submitting feedback, missing PHID or PHEID");
    return;
  }

  if (!firestore) {
    toast.error("Firestore not initialized");
    return;
  }

  console.debug("Submitting feedback for entry", feedbackData.powerHourEntryId, "in Power Hour", feedbackData.powerHourId)

  const fdbk = feedbackData.feedback;

  try {
    const docRef = doc(firestore, "power-hour-feedback", feedbackData.powerHourId, "entries", feedbackData.powerHourEntryId);
    runTransaction(firestore, async (transaction) => {
      const document = await transaction.get(docRef);

      if (!document.exists()) {
        transaction.set(docRef, feedbackData, { merge: true });
      } else {
        await transaction.update(docRef, {
          ["upvotes"]: increment(feedbackData.upvotes ?? 0),
          ["downvotes"]: increment(feedbackData.downvotes ?? 0),
          [`feedback.${PHEFeedbackOpts.ProblemWithPlayback}`]: increment(fdbk && fdbk[PHEFeedbackOpts.ProblemWithPlayback] ? fdbk[PHEFeedbackOpts.ProblemWithPlayback] : 0),
          [`feedback.${PHEFeedbackOpts.ProblemWithSong}`]: increment(fdbk && fdbk![PHEFeedbackOpts.ProblemWithSong] ? fdbk![PHEFeedbackOpts.ProblemWithSong] : 0),
          [`feedback.${PHEFeedbackOpts.ProblemWithVideo}`]: increment(fdbk && fdbk![PHEFeedbackOpts.ProblemWithVideo] ? fdbk![PHEFeedbackOpts.ProblemWithVideo] : 0),
          [`feedback.${PHEFeedbackOpts.SongDoesntMatchTitle}`]: increment(fdbk && fdbk![PHEFeedbackOpts.SongDoesntMatchTitle] ? fdbk![PHEFeedbackOpts.SongDoesntMatchTitle] : 0),
          [`feedback.${PHEFeedbackOpts.SongDoesntMatchVideo}`]: increment(fdbk && fdbk![PHEFeedbackOpts.SongDoesntMatchVideo] ? fdbk![PHEFeedbackOpts.SongDoesntMatchVideo] : 0),
          [`feedback.${PHEFeedbackOpts.SongIsntMusic}`]: increment(fdbk && fdbk[PHEFeedbackOpts.SongIsntMusic] ? fdbk[PHEFeedbackOpts.SongIsntMusic] : 0),
          [`feedback.${PHEFeedbackOpts.VideoIsntMusicVideo}`]: increment(fdbk && fdbk[PHEFeedbackOpts.VideoIsntMusicVideo] ? fdbk[PHEFeedbackOpts.VideoIsntMusicVideo] : 0)
        });
      }
    });
    console.log("Feedback submitted successfully");

  } catch (error) {
    console.error("Error submitting feedback:", error);
    throw new Error("Error submitting feedback: " + error);
  }
};


export const failedToPlay = async (powerHourId: string, entryId: string) => {
  if (!powerHourId || !entryId) {
    toast.error("Error submitting feedback, missing PHID or PHEID");
    return;
  }
  const feedbackData: Partial<PHEntryFeedback> = {
    powerHourId: powerHourId,
    powerHourEntryId: entryId,
    feedback: {
      [PHEFeedbackOpts.ProblemWithPlayback]: 1
    }
  }
  submitFeedbackPowerHourEntry(feedbackData);
}

export const upVoteEntry = async (powerHourId: string, entryId: string) => {
  if (!powerHourId || !entryId) {
    toast.error("Error submitting feedback, missing PHID or PHEID");
    return;
  }
  const feedbackData: Partial<PHEntryFeedback> = {
    powerHourId: powerHourId,
    powerHourEntryId: entryId,
    upvotes: 1
  }
  submitFeedbackPowerHourEntry(feedbackData);
}

export const downVoteEntry = async (powerHourId: string, entryId: string) => {
  if (!powerHourId || !entryId) {
    toast.error("Error submitting feedback, missing PHID or PHEID");
    return;
  }
  const feedbackData: Partial<PHEntryFeedback> = {
    powerHourId: powerHourId,
    powerHourEntryId: entryId,
    downvotes: 1
  }
  submitFeedbackPowerHourEntry(feedbackData);
}
