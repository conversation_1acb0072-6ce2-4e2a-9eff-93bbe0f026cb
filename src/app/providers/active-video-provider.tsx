"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import YouTube from "react-youtube";

// Define the YouTubePlayer type manually since it's not exported from react-youtube
type YouTubePlayer = {
  playVideo: () => void;
  pauseVideo: () => void;
  seekTo: (seconds: number) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  [key: string]: any;
};

export const ActiveVideoContext = createContext({
  playing: false,
  startTime: 0,
  endTime: 0,
  currentTime: 0,
  videoIsReady: false,
  playerIsReady: false,
  playerRef: { current: null } as React.RefObject<YouTubePlayer | null>,
  setPlaying: (isPlaying: boolean) => {},
  setPlayerIsReady: (isReady: boolean) => {},
  setVideoIsReady: (isReady: boolean) => {},
  setCurrentTime: (time: number) => {},
  askToToggle: (shouldPlay?: boolean) => {},
});

export function useActiveVideo(): React.ContextType<typeof ActiveVideoContext> {
  return useContext(ActiveVideoContext);
}

export function ActiveVideoProvider({
  children,
  canPlay,
  videoStartTime,
  videoEndTime,
}: {
  children: React.ReactNode;
  canPlay: boolean;
  videoStartTime: number;
  videoEndTime: number;
}) {
  const [playing, setPlaying] = useState(false); // this is the actually state of the video given from the player
  const [currentTime, setCurrentTime] = useState(0);
  const [videoIsReady, setVideoIsReady] = useState(false);
  const [playerIsReady, setPlayerIsReady] = useState(false);
  const playerRef = useRef<YouTubePlayer | null>(null);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (playing) {
        console.log("tick");
        setCurrentTime((tick) => tick + 1); // Increment the tick
      }
    }, 1000); // Set interval for 1 second

    return () => clearInterval(intervalId); // Clear interval on component unmount
  }, [playing]);

  const askToToggle = (shouldPlay?: boolean): boolean => {
    let status = playing;
    if (!canPlay) {
      console.warn("Cannot play yet");
      return status;
    }

    if (shouldPlay !== undefined && shouldPlay !== null) {
      if (shouldPlay && playerRef?.current && playerRef.current.playVideo) {
        playerRef.current.playVideo();
      } else if (
        !shouldPlay &&
        playerRef?.current &&
        playerRef.current.pauseVideo
      ) {
        playerRef.current.pauseVideo();
      }
      console.log("togglePlaying", shouldPlay);
      status = shouldPlay;
    } else {
      console.log("togglePlaying", !playing);
      if (!playing && playerRef?.current && playerRef.current.playVideo) {
        playerRef.current.playVideo();
      } else if (
        playing &&
        playerRef?.current &&
        playerRef.current.pauseVideo
      ) {
        playerRef.current.pauseVideo();
      }
      status = !playing;
    }
    return status;
  };

  return (
    <ActiveVideoContext.Provider
      value={{
        videoIsReady,
        setVideoIsReady,
        playing,
        setPlaying,
        askToToggle,
        currentTime,
        setCurrentTime,
        playerIsReady,
        setPlayerIsReady,
        startTime: videoStartTime,
        endTime: videoEndTime,
        playerRef: playerRef,
      }}
    >
      {children}
    </ActiveVideoContext.Provider>
  );
}
