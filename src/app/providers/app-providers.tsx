"use client";

import React, { ReactNode } from 'react';
import { NextUIProvider } from "@nextui-org/react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { CastingProvider } from "./casting-manager";

export interface AppProvidersProps {
  children: ReactNode;
}

export const AppProviders = ({ children }: AppProvidersProps) => {
  return (
    <NextUIProvider>
      <NextThemesProvider attribute="class" defaultTheme="dark">
        <CastingProvider>{children}</CastingProvider>
      </NextThemesProvider>
    </NextUIProvider>
  );
};

export default AppProviders;
