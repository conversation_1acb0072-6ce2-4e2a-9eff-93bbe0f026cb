import { useState, useEffect } from 'react';
import { DocumentSnapshot } from 'firebase/firestore';
import { firestore } from '../firebase/firebase';
import { PHEntryFeedback, PHFeedback } from '../../models/feedback';
import { failedToPlay, fetchFeedbackPowerHour, fetchFeedbackPowerHourEntryList, submitFeedbackPowerHour, downVoteEntry, upVoteEntry } from './feedback';
import { toast } from 'react-toastify';
import _ from 'lodash';


function useFeedback(powerHourId: string) {
  // upvotes and downvotes are directly on power hour
  const [loading, setLoading] = useState(true);
  const [feedback, setFeedback] = useState({} as PHFeedback);
  const [entriesFeedback, setEntriesFeedback] = useState([] as PHEntryFeedback[]);
  const [sessionFeedback, setSessionFeedback] = useState({} as PHFeedback);


  useEffect(() => {
    setLoading(true);
    powerHourId && fetchFeedbackPowerHour(powerHourId).then((feedback: PHFeedback) => {
      setFeedback(feedback);
      setLoading(false);
    }).catch((error) => {
      console.error("Error fetching feedback:", error);
      setLoading(false);
    }).finally(() => {
      setLoading(false);
    });

  }, [powerHourId]);

  useEffect(() => {
    setLoading(true);
    powerHourId && fetchFeedbackPowerHourEntryList(powerHourId).then((entriesFeedback: PHEntryFeedback[]) => {
      setEntriesFeedback(entriesFeedback);
    }).catch((error) => {
      console.error("Error fetching feedback entries:", error);
    }).finally(() => {
      setLoading(false);
    });

  }, [powerHourId]);


  useEffect(() => {
    if (powerHourId && Object.keys(sessionFeedback).length > 0) {
      submitFeedbackPowerHour({ ...sessionFeedback, powerHourId }).then(() => {
        toast.success("Feedback submitted!");
      }).catch((error) => {
        console.error("Error submitting feedback:", error);
      });
    }
  }, [sessionFeedback]);


  return { loading, feedback, entriesFeedback, failedToPlay, sessionFeedback, setSessionFeedback, upVoteEntry, downVoteEntry };
}

export default useFeedback;