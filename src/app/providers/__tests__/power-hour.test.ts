import { toast } from 'react-toastify';
import { 
  fetchPowerHour, 
  updatePowerHour, 
  setPowerHour, 
  createPowerHour,
  fetchAllPowerHours, 
  fetchTopPowerHours 
} from '../power-hour';
import { PowerHourGenerationSteps } from '@/models/power-hour';

// Mock the needed modules directly instead of using the firebase-mock file
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
  },
}));

// Define mock functions first before jest.mock calls
const mockDoc = jest.fn().mockReturnValue({ id: 'test-id' });
const mockCollection = jest.fn().mockReturnValue({ id: 'mock-collection-id' });

const mockDocSnap = {
  exists: jest.fn().mockReturnValue(true),
  data: jest.fn().mockReturnValue({
    id: 'test-id',
    title: 'Test Power Hour',
    songs: [{ id: 'song1', title: 'Test Song' }],
    entries: [{ id: 'entry1', songId: 'song1', videoId: 'video1' }],
    currentStep: PowerHourGenerationSteps.Complete
  }),
  id: 'test-id',
};

const mockGetDoc = jest.fn().mockResolvedValue(mockDocSnap);
const mockSetDoc = jest.fn().mockResolvedValue(undefined);
const mockUpdateDoc = jest.fn().mockResolvedValue(undefined);
const mockAddDoc = jest.fn().mockResolvedValue({ id: 'new-doc-id' });

const mockQuerySnap = {
  empty: false,
  docs: [
    {
      id: 'mock-doc-id-1',
      data: () => ({ title: 'Mock Power Hour 1' }),
      exists: () => true,
    },
    {
      id: 'mock-doc-id-2',
      data: () => ({ title: 'Mock Power Hour 2' }),
      exists: () => true,
    },
  ],
  forEach: jest.fn(),
};

const mockGetDocs = jest.fn().mockResolvedValue(mockQuerySnap);
const mockQuery = jest.fn().mockReturnValue('mock-query');
const mockOrderBy = jest.fn().mockReturnValue('mock-orderBy-constraint');
const mockLimit = jest.fn().mockReturnValue('mock-limit-constraint');

// Now use the mocks in jest.mock calls
jest.mock('../../../app/firebase/firebase', () => ({
  COLLECTIONS: {
    POWER_HOURS: 'power-hours',
    FEEDBACK: 'feedback',
  },
  firestore: { app: {} },
  isFirebaseAvailable: jest.fn().mockReturnValue(true),
}));

jest.mock('firebase/firestore', () => ({
  doc: mockDoc,
  collection: mockCollection,
  getDoc: mockGetDoc,
  getDocs: mockGetDocs,
  setDoc: mockSetDoc,
  updateDoc: mockUpdateDoc,
  addDoc: mockAddDoc,
  query: mockQuery,
  where: jest.fn().mockReturnValue('mock-where-constraint'),
  orderBy: mockOrderBy,
  limit: mockLimit,
}));

// Toast is already mocked above

describe('Power Hour Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up common mock return values
    mockDocSnap.exists.mockReturnValue(true);
    mockDocSnap.data.mockReturnValue({
      id: 'test-id',
      title: 'Test Power Hour',
      songs: [{ id: 'song1', title: 'Test Song' }],
      entries: [{ id: 'entry1', songId: 'song1', videoId: 'video1' }],
      currentStep: PowerHourGenerationSteps.Complete
    });
  });

  describe('fetchPowerHour', () => {
    it('should fetch a power hour by id', async () => {
      const result = await fetchPowerHour('test-id');
      
      expect(mockDoc).toHaveBeenCalled();
      expect(mockGetDoc).toHaveBeenCalled();
      expect(result).toEqual({
        id: 'test-id',
        title: 'Test Power Hour',
        songs: [{ id: 'song1', title: 'Test Song' }],
        entries: [{ id: 'entry1', songId: 'song1', videoId: 'video1' }],
        currentStep: PowerHourGenerationSteps.Complete
      });
    });

    it('should return null when document does not exist', async () => {
      mockDocSnap.exists.mockReturnValueOnce(false);
      
      const result = await fetchPowerHour('non-existent-id');
      
      expect(result).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Power hour not found');
    });

    it('should handle errors when fetching a power hour', async () => {
      mockGetDoc.mockImplementationOnce(() => Promise.reject(new Error('Test error')));
      
      const result = await fetchPowerHour('test-id');
      
      expect(result).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Error fetching power hour: Test error');
    });
  });

  describe('updatePowerHour', () => {
    it('should update a power hour', async () => {
      const powerHour = {
        id: 'test-id',
        title: 'Updated Power Hour',
        lastUpdateTime: Date.now()
      };
      
      const result = await updatePowerHour(powerHour);
      
      expect(mockDoc).toHaveBeenCalled();
      expect(mockUpdateDoc).toHaveBeenCalled();
      expect(result).toBe('test-id');
    });

    it('should handle errors when updating a power hour', async () => {
      mockUpdateDoc.mockImplementationOnce(() => Promise.reject(new Error('Update error')));
      
      const powerHour = {
        id: 'test-id',
        title: 'Updated Power Hour'
      };
      
      const result = await updatePowerHour(powerHour);
      
      expect(result).toBe('test-id');
      expect(toast.error).toHaveBeenCalled();
    });
  });

  describe('setPowerHour', () => {
    it('should set a power hour document', async () => {
      const powerHour = {
        id: 'test-id',
        title: 'New Power Hour',
        songs: [],
        entries: []
      };
      
      const result = await setPowerHour(powerHour);
      
      expect(mockDoc).toHaveBeenCalled();
      expect(mockSetDoc).toHaveBeenCalled();
      expect(result).toBe('test-id');
    });

    it('should create a new power hour document if no ID is provided', async () => {
      const powerHour = {
        title: 'Brand New Power Hour',
        songs: [],
        entries: []
      };
      
      const result = await setPowerHour(powerHour);
      
      expect(mockCollection).toHaveBeenCalled();
      expect(mockAddDoc).toHaveBeenCalled();
      expect(result).toBe('new-doc-id');
    });

    it('should handle errors when setting a power hour', async () => {
      mockSetDoc.mockImplementationOnce(() => Promise.reject(new Error('Set error')));
      
      const powerHour = {
        id: 'test-id',
        title: 'New Power Hour'
      };
      
      const result = await setPowerHour(powerHour);
      
      expect(result).toBeNull();
      expect(toast.error).toHaveBeenCalled();
    });
  });

  describe('createPowerHour', () => {
    it('should create a new power hour', async () => {
      // Create a proper PowerHour object for the test
      const powerHourObject = {
        id: '',
        title: 'Test Power Hour',
        search: 'Test Search',
        count: 10,
        songs: [],
        entries: []
      };
      
      const result = await createPowerHour(powerHourObject);
      
      expect(mockAddDoc).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          title: 'Test Power Hour',
          search: 'Test Search'
        })
      );
      expect(result).toBe('new-doc-id');
    });

    it('should handle errors when creating a power hour', async () => {
      mockAddDoc.mockImplementationOnce(() => Promise.reject(new Error('Creation error')));
      
      // Create a proper PowerHour object for the test
      const powerHourObject = {
        id: '',
        title: 'Test Power Hour',
        search: 'Test Search',
        count: 10,
        songs: [],
        entries: []
      };
      
      const result = await createPowerHour(powerHourObject);
      
      expect(result).toBeNull();
      expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('Creation error'));
    });
  });

  describe('fetchAllPowerHours', () => {
    it('should fetch all power hours', async () => {
      const result = await fetchAllPowerHours();
      
      expect(mockCollection).toHaveBeenCalled();
      expect(mockGetDocs).toHaveBeenCalled();
      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Mock Power Hour 1');
      expect(result[1].title).toBe('Mock Power Hour 2');
    });

    it('should handle errors when fetching all power hours', async () => {
      mockGetDocs.mockImplementationOnce(() => Promise.reject(new Error('Fetch error')));
      
      const result = await fetchAllPowerHours();
      
      expect(result).toEqual([]);
      expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('Fetch error'));
    });
  });

  describe('fetchTopPowerHours', () => {
    it('should fetch top power hours with given limit', async () => {
      const result = await fetchTopPowerHours(5);
      
      expect(mockCollection).toHaveBeenCalled();
      expect(mockQuery).toHaveBeenCalled();
      expect(mockOrderBy).toHaveBeenCalledWith('upvotes', 'desc');
      expect(mockLimit).toHaveBeenCalledWith(5);
      expect(mockGetDocs).toHaveBeenCalled();
      expect(result).toHaveLength(2);
    });

    it('should handle errors when fetching top power hours', async () => {
      mockGetDocs.mockImplementationOnce(() => Promise.reject(new Error('Top fetch error')));
      
      const result = await fetchTopPowerHours(3);
      
      expect(result).toEqual([]);
      expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('Top fetch error'));
    });
  });
});
