@tailwind base;
@tailwind components;
@tailwind utilities;

@import "~react-toastify/dist/ReactToastify.css";

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  height:100vh;
  width:100vw;
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

 * {
  box-sizing: border-box;
 }

 /* Custom scrollbar for Chrome, Safari, and Edge */
::-webkit-scrollbar {
  width: 12px; /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: #2e2e2e; /* Color of the tracking area */
}

::-webkit-scrollbar-thumb {
  background-color: #888; /* Color of the scroll thumb */
  border-radius: 6px; /* Roundness of the scroll thumb */
  border: 3px solid #2e2e2e; /* Creates padding around the scroll thumb */
}

/* Custom scrollbar for Firefox */
* {
  scrollbar-width: thin; /* "auto" or "thin" */
  scrollbar-color: #888 #2e2e2e; /* thumb and track color */
}

/* Mobile-specific styles for better touch interactions */
@media (max-width: 768px) {
  /* Make feedback buttons more accessible on touch devices */
  .group:hover .opacity-0 {
    opacity: 0.7; /* Show feedback buttons slightly on mobile */
  }

  /* Improve touch targets */
  button {
    min-height: 44px; /* iOS recommended minimum touch target */
    min-width: 44px;
  }

  /* Better spacing for mobile cards */
  .mobile-card-spacing {
    margin-bottom: 1rem;
  }
}

/* Touch device specific styles */
@media (hover: none) and (pointer: coarse) {
  /* Show feedback overlay on touch devices */
  .group .opacity-0 {
    opacity: 0.3;
    transition: opacity 0.3s ease;
  }

  .group:active .opacity-0 {
    opacity: 1;
  }
}
