 const PHE = {
  "id": "4NRXx6U8ABQ_blindinglights-theweeknd",
  "idealEndTime": 97,
  "idealStartTime": 37,
  "song": {
    "album": "After Hours",
    "artist": "The Weeknd",
    "genre": "Pop",
    "id": "blindinglights-theweeknd",
    "idealEndTime": 97,
    "idealStartTime": 37,
    "title": "Blinding Lights",
    "year": 2019
  },
  "songId": "blindinglights-theweeknd",
  "video": {
    "artist": "the weeknd",
    "description": "Official music video for The Weeknd \"Blinding Lights\" - available everywhere now: http://theweeknd.co/blindinglightsYD ...",
    "id": "4NRXx6U8ABQ",
    "provider": "YouTube",
    "songId": "blindinglights-theweeknd",
    "thumbnail": "https://i.ytimg.com/vi/4NRXx6U8ABQ/default.jpg",
    "title": "The Weeknd - Blinding Lights (Official Video)",
    "url": "https://www.youtube.com/watch?v=4NRXx6U8ABQ",
    "videoStatus": {
      "status": "Available",
      "timeVerified": 1709490784091,
      "videoId": "4NRXx6U8ABQ"
    }
  },
  "videoId": "4NRXx6U8ABQ",
  "videoStatus": {
    "status": "Available",
    "timeVerified": 1709490784091,
    "videoId": "4NRXx6U8ABQ"
  },
  stats: {}
}

export default PHE;