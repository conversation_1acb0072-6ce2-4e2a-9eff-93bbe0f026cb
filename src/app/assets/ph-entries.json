[{"id": "4NRXx6U8ABQ_blindinglights-theweeknd", "idealEndTime": 97, "idealStartTime": 37, "song": {"album": "After Hours", "artist": "The Weeknd", "genre": "pop", "id": "blindinglights-theweeknd", "idealEndTime": 97, "idealStartTime": 37, "title": "Blinding Lights", "year": "2019"}, "songId": "blindinglights-theweeknd", "video": {"artist": "the weeknd", "description": "Official music video for The Weeknd \"Blinding Lights\" - available everywhere now: http://theweeknd.co/blindinglightsYD ...", "id": "4NRXx6U8ABQ", "provider": "YouTube", "songId": "blindinglights-theweeknd", "thumbnail": "https://i.ytimg.com/vi/4NRXx6U8ABQ/default.jpg", "title": "The Weeknd - Blinding Lights (Official Video)", "url": "https://www.youtube.com/watch?v=4NRXx6U8ABQ", "videoStatus": {"status": "Available", "timeVerified": 1709490784091, "videoId": "4NRXx6U8ABQ"}}, "videoId": "4NRXx6U8ABQ", "videoStatus": {"status": "Available", "timeVerified": 1709490784091, "videoId": "4NRXx6U8ABQ"}}, {"id": "ZmDBbnmKpqQ_driverslicense-oliviarodrigo", "idealEndTime": 94, "idealStartTime": 34, "song": {"album": "SOUR", "artist": "<PERSON>", "genre": "pop", "id": "driverslicense-oliviarodrigo", "idealEndTime": 94, "idealStartTime": 34, "title": "drivers license", "year": "2021"}, "songId": "driverslicense-oliviarodrigo", "video": {"artist": "olivia rod<PERSON>o", "description": "Listen to 'drivers license' out now: https://smarturl.it/driverslicense Pre-order the debut album SOUR: ...", "id": "ZmDBbnmKpqQ", "provider": "YouTube", "songId": "driverslicense-oliviarodrigo", "thumbnail": "https://i.ytimg.com/vi/ZmDBbnmKpqQ/default.jpg", "title": "<PERSON> - drivers license (Official Video)", "url": "https://www.youtube.com/watch?v=ZmDBbnmKpqQ", "videoStatus": {"status": "Available", "timeVerified": 1709490784599, "videoId": "ZmDBbnmKpqQ"}}, "videoId": "ZmDBbnmKpqQ", "videoStatus": {"status": "Available", "timeVerified": 1709490784599, "videoId": "ZmDBbnmKpqQ"}}, {"id": "TUVcZfQe-Kw_levitating-dua-lipa", "idealEndTime": 99, "idealStartTime": 39, "song": {"album": "Future Nostalgia", "artist": "<PERSON><PERSON>", "genre": "pop", "id": "levitating-dua-lipa", "idealEndTime": 99, "idealStartTime": 39, "title": "Levitating", "year": "2020"}, "songId": "levitating-dua-lipa", "video": {"artist": "dua lipa", "description": "The official music video for <PERSON><PERSON> - <PERSON> featuring @Babyjesus704 Taken from her second studio album 'Future ...", "id": "TUVcZfQe-Kw", "provider": "YouTube", "songId": "levitating-dua-lipa", "thumbnail": "https://i.ytimg.com/vi/TUVcZfQe-Kw/default.jpg", "title": "<PERSON><PERSON> - <PERSON><PERSON> Featuring <PERSON><PERSON><PERSON> (Official Music Video)", "url": "https://www.youtube.com/watch?v=TUVcZfQe-Kw", "videoStatus": {"status": "Available", "timeVerified": 1709490784896, "videoId": "TUVcZfQe-Kw"}}, "videoId": "TUVcZfQe-Kw", "videoStatus": {"status": "Available", "timeVerified": 1709490784896, "videoId": "TUVcZfQe-Kw"}}, {"id": "E07s5ZYygMg_watermelon-sugar-harrystyles", "idealEndTime": 94, "idealStartTime": 34, "song": {"album": "Fine Line", "artist": "<PERSON>", "genre": "pop", "id": "watermelon-sugar-harrystyles", "idealEndTime": 94, "idealStartTime": 34, "title": "Watermelon Sugar", "year": "2019"}, "songId": "watermelon-sugar-harrystyles", "video": {"artist": "harry styles", "description": "This video is dedicated to touching. Listen to <PERSON>' new album 'Fine Line' now: https://HStyles.lnk.to/FineLineAY Follow ...", "id": "E07s5ZYygMg", "provider": "YouTube", "songId": "watermelon-sugar-harrystyles", "thumbnail": "https://i.ytimg.com/vi/E07s5ZYygMg/default.jpg", "title": "<PERSON> - Watermel<PERSON> (Official Video)", "url": "https://www.youtube.com/watch?v=E07s5ZYygMg", "videoStatus": {"status": "Available", "timeVerified": 1709490785348, "videoId": "E07s5ZYygMg"}}, "videoId": "E07s5ZYygMg", "videoStatus": {"status": "Available", "timeVerified": 1709490785348, "videoId": "E07s5ZYygMg"}}, {"id": "tcYodQoapMg_positions-a<PERSON><PERSON><PERSON>e", "idealEndTime": 93, "idealStartTime": 33, "song": {"album": "Positions", "artist": "<PERSON><PERSON>", "genre": "pop", "id": "positions-<PERSON><PERSON><PERSON><PERSON><PERSON>", "idealEndTime": 93, "idealStartTime": 33, "title": "positions", "year": "2020"}, "songId": "positions-<PERSON><PERSON><PERSON><PERSON><PERSON>", "video": {"artist": "ariana grande", "description": "The official “positions” music video by <PERSON><PERSON>. Listen & download the song here: http://arianagrande.lnk.to/positions ...", "id": "tcYodQoapMg", "provider": "YouTube", "songId": "positions-<PERSON><PERSON><PERSON><PERSON><PERSON>", "thumbnail": "https://i.ytimg.com/vi/tcYodQoapMg/default.jpg", "title": "<PERSON><PERSON> - positions (official video)", "url": "https://www.youtube.com/watch?v=tcYodQoapMg", "videoStatus": {"status": "Available", "timeVerified": 1709490785696, "videoId": "tcYodQoapMg"}}, "videoId": "tcYodQoapMg", "videoStatus": {"status": "Available", "timeVerified": 1709490785696, "videoId": "tcYodQoapMg"}}]