import React, { useState, useEffect } from "react";
import {
  doc,
  collection,
  setDoc,
  getDocs,
  query,
  orderBy,
} from "firebase/firestore";
import { firestore, COLLECTIONS } from "@/app/firebase/firebase"; // Using main firestore export

interface ChatCompletionMessageParam {
  content: string;
  role: "user" | "system";
}

export interface Prompt {
  id?: string;
  version: string;
  messages: ChatCompletionMessageParam[];
}

const compareSemVer = (a: string, b: string) => {
  const pa = a.split("-").map((num) => parseInt(num, 10));
  const pb = b.split("-").map((num) => parseInt(num, 10));
  for (let i = 0; i < 3; i++) {
    if (pa[i] > pb[i]) return 1;
    if (pa[i] < pb[i]) return -1;
  }
  return 0;
};

const PromptManager: React.FC = () => {
  const [version, setVersion] = useState<string>("1.0.0");
  const [newUserRole, setNewUserRole] = useState<"user" | "system">("user");
  const [newMessageText, setNewMessageText] = useState<string>("");
  const [messages, setMessages] = useState<ChatCompletionMessageParam[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);

  // useEffect(() => {
  //   const promptsRef = ref(database, '/prompts/');
  //   onValue(promptsRef, (snapshot) => {
  //     const data = snapshot.val();
  //     const loadedPrompts: Prompt[] = data ? Object.keys(data).map(key => ({
  //       id: key,
  //       ...data[key],
  //     })) : [];
  //     setPrompts(loadedPrompts);
  //   });
  // }, []);

  useEffect(() => {
    if (!firestore) return;
    
    // Use a hardcoded collection name since it's not defined in COLLECTIONS
    const PROMPTS_COLLECTION = "prompts";
    
    // Use Firestore instead of RTDB
    const promptsCollection = collection(firestore, PROMPTS_COLLECTION);
    const promptsQuery = query(promptsCollection, orderBy("version", "desc"));

    getDocs(promptsQuery)
      .then((snapshot) => {
        if (!snapshot.empty) {
          const firstDoc = snapshot.docs[0];
          const data = firstDoc.data();
          setVersion(data.version);
          setMessages(data.messages || []);
        }
      })
      .catch((error) => {
        console.error("Error loading prompts from Firestore:", error);
      });
  }, []);

  const handleAddMessage = () => {
    if (!newMessageText || !newUserRole) return; // Basic validation

    const newMessage: ChatCompletionMessageParam = {
      content: newMessageText,
      role: newUserRole,
    };
    setMessages((prevMessages) => [...prevMessages, newMessage]);
    setNewMessageText("");
    // Optionally reset user role to default
  };

  const handleDeleteMessage = (index: number) => {
    setMessages(messages.filter((_, i) => i !== index));
  };

  const isSubmitDisabled = !newMessageText || !newUserRole;

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!firestore) return;
    if (messages.length === 0) return; // Ensure there's at least one message

    const newPrompt: Omit<Prompt, "id"> = {
      version,
      messages,
    };
    // Use a version-formatted document ID
    const docId = `prompt-${version.replace(/\./g, "-")}`;
    // Use a hardcoded collection name since it's not defined in COLLECTIONS
    const PROMPTS_COLLECTION = "prompts";
    const promptDoc = doc(firestore, PROMPTS_COLLECTION, docId);

    setDoc(promptDoc, newPrompt)
      .then(() => {
        console.log("Prompt saved to Firestore");
      })
      .catch((error) => {
        console.error("Error saving prompt to Firestore:", error);
      });
  };

  return (
    <div className="p-4 w-screen h-full flex flex-col">
      {messages.map((msg, index) => (
        <div
          key={index}
          className="mb-2 flex justify-between items-center text-white bg-gray-100 p-2 rounded"
        >
          <div>
            <p className="text-gray-600">{msg.role}</p>
            <p className="text-gray-800">{msg.content}</p>
          </div>
          <button
            onClick={() => handleDeleteMessage(index)}
            className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
          >
            Delete
          </button>
        </div>
      ))}
      <form onSubmit={handleSubmit} className="mt-auto">
        <select
          value={newUserRole}
          onChange={(e) => setNewUserRole(e.target.value as "system" | "user")}
          className="w-full mb-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black"
        >
          <option value="system">System</option>
          <option value="user">User</option>
        </select>
        <textarea
          value={newMessageText}
          onChange={(e) => setNewMessageText(e.target.value)}
          placeholder="Enter new message content"
          className="w-full mb-2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-black"
          rows={3}
        />
        <div className="flex justify-between items-center">
          <input
            type="text"
            value={version}
            onChange={(e) => setVersion(e.target.value)}
            placeholder="Enter version (SemVer)"
            className=" px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 flex-grow mr-2 text-black w-40"
          />
          <button
            type="button"
            onClick={handleAddMessage}
            disabled={isSubmitDisabled}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
          >
            Add Message
          </button>
          <button
            type="submit"
            disabled={messages.length === 0}
            className="ml-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-700 disabled:bg-indigo-300"
          >
            Submit Prompt
          </button>
        </div>
      </form>
    </div>
  );
};

export default PromptManager;
