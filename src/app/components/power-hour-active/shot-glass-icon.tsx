/** @jsxImportSource @emotion/react */
import React, { useEffect, useState } from "react";
import { css, keyframes } from "@emotion/react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

export const ShotGlassIcon = ({ ...props }) => {
	const gifUrl = "/images/shots.gif";

	return (
		<motion.div
			key="shot-glass-icon"
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 3 }}
			className="flex items-center justify-center"
			style={{
				borderRadius: "50%",
				height: "75px",
				width: "75px",
				background: "white",
			}}
		>
			{props.progress < 50 ? (
				<motion.div
					key="shot-glass-icon"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ duration: 3 }}
				>
					<span className="text-3xl text-black flex h-50 w-50">
						{props.count}
					</span>
				</motion.div>
			) : (
				<motion.div
					key="shot-glass-icon"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ duration: 3 }}
				>
					<Image src={gifUrl} alt="shots" width={50} height={50} />
				</motion.div>
			)}
		</motion.div>
	);
};
