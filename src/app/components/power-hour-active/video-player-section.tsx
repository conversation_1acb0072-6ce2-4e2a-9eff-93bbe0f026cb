"use client";
import React from 'react';
import { motion } from "framer-motion";
import YoutubePlayer from "./youtube-player";

interface VideoPlayerSectionProps {
  videoId: string;
  startTime: number;
  endTime: number;
  onSongEnd: (dueToError?: boolean) => void;
  onSongError: () => void;
  castingMode: boolean;
}

const VideoPlayerSection: React.FC<VideoPlayerSectionProps> = ({
  videoId,
  startTime = 0,
  endTime = 60,
  onSongEnd,
  onSongError,
  castingMode = false
}) => {
  if (!videoId) return null;

  return (
    <motion.div
      className="relative w-full rounded-xl overflow-hidden shadow-2xl mx-auto"
      style={{
        height: "min(calc(100vh - 420px), 500px)",
        aspectRatio: "16/9",
        maxWidth: "100%",
      }}
      initial={{ scale: 0.95, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
    >
      <YoutubePlayer
        videoId={videoId}
        startTime={startTime}
        endTime={endTime}
        onSongEnd={onSongEnd}
        onSongError={onSongError}
        castingMode={castingMode}
      />
    </motion.div>
  );
};

export default VideoPlayerSection;
