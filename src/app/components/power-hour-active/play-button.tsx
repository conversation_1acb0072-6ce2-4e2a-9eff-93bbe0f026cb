import { But<PERSON> } from "@nextui-org/button";
import React from "react";
import { FaPlay, FaPause } from "react-icons/fa";

export function PlayButton({
	onToggle,
	playing,
	...props
}: {
	onToggle: (shouldplay?: any) => any;
	playing: boolean;
	color?: any;
	className?: string;
}) {
	return (
		<Button
			onClick={() => onToggle(!playing)}
			className={props.className}
			isIconOnly
			color={props.color}
			variant="ghost"
			aria-label="Play or Pause"
		>
			{playing ? <FaPause /> : <FaPlay />}
		</Button>
	);
}
