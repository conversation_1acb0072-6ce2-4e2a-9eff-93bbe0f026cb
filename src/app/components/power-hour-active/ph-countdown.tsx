import { useActiveVideo } from "@/app/providers/active-video-provider";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

const countdownVariants = {
	initial: { scale: 0, opacity: 0, rotate: -10 },
	enter: { scale: 1, opacity: 1, rotate: 0, transition: { type: "spring", stiffness: 300, damping: 15 } },
	exit: { scale: 0, opacity: 0, rotate: 10, transition: { duration: 0.3 } }
};

const backdropVariants = {
	initial: { opacity: 0 },
	enter: { opacity: 1, transition: { duration: 0.3 } },
	exit: { opacity: 0, transition: { duration: 0.5, delay: 0.2 } }
};

export const PHCountdown = ({
	autoStart = true,
	onTimerComplete,
	castingMode = false
}: {
	autoStart?: boolean;
	onTimerComplete?: () => any;
	castingMode?: boolean;
}) => {
	const [countdown, setCountdown] = useState(3);
	const [isVisible, setIsVisible] = useState(true);
	const { askToToggle } = useActiveVideo();

	const countdownMessages = [
		"Get Ready!",
		"3",
		"2",
		"1",
		"Let's Go!"
	];

	useEffect(() => {
		if (!autoStart) return;
		
		let timerId: NodeJS.Timeout | null = null;
		
		if (countdown >= 0 && isVisible) {
			timerId = setTimeout(() => {
				if (countdown === 0) {
					// Start playback and hide countdown
					askToToggle(true);
					setTimeout(() => setIsVisible(false), 1000);
					if (onTimerComplete) onTimerComplete();
				} else {
					setCountdown(countdown - 1);
				}
			}, 1000);
		}
		
		return () => {
			if (timerId) clearTimeout(timerId);
		};
	}, [countdown, isVisible, autoStart, askToToggle, onTimerComplete]);

	return (
		<AnimatePresence>
			{isVisible && (
				<motion.div
					className="fixed z-50 inset-0 flex items-center justify-center"
					key="countdown-backdrop"
					variants={backdropVariants}
					initial="initial"
					animate="enter"
					exit="exit"
				>
					{/* Blurred backdrop with gradient */}
					<div className="absolute inset-0 bg-gradient-to-b from-gray-900/90 via-gray-900/95 to-black backdrop-blur-sm" />
					
					{/* Main countdown content */}
					<div className="relative flex flex-col items-center">
						{/* Mode indicator */}
						<motion.div 
							className="mb-10"
							initial={{ opacity: 0, y: -20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: 0.5 }}
						>
							<span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${castingMode ? 'bg-orange-200 text-orange-800' : 'bg-blue-200 text-blue-800'} shadow-lg`}>
								{castingMode ? 'Casting to Device' : 'Playing on This Device'}
							</span>
						</motion.div>
						
						{/* Countdown number */}
						<AnimatePresence mode="wait">
							<motion.div
								key={countdown}
								className="relative"
								variants={countdownVariants}
								initial="initial"
								animate="enter"
								exit="exit"
							>
								{/* Rings around the countdown */}
								<div className="absolute inset-0 -z-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 blur-2xl opacity-20 animate-pulse" />
								<div className="absolute inset-2 -z-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 blur-md opacity-30" />
								
								<div className={`${countdown === 0 ? 'text-green-400' : countdown === 3 ? 'text-yellow-400' : 'text-white'} text-7xl md:text-9xl font-bold rounded-full h-48 w-48 flex items-center justify-center bg-gray-900/70 border-2 ${countdown === 0 ? 'border-green-500' : 'border-gray-700'} shadow-2xl`}>
									{countdownMessages[3 - countdown] || countdownMessages[4]}
								</div>
							</motion.div>
						</AnimatePresence>
						
						{/* Instruction text */}
						<motion.p 
							className="text-gray-300 mt-10 text-lg"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ delay: 0.5 }}
						>
							Get your drinks ready!
						</motion.p>
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	);
};

export default PHCountdown;
