"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON><PERSON>er, <PERSON>dal<PERSON>ody, ModalFooter, useDisclosure } from '@nextui-org/react';
import { FaChromecast, FaTv, FaLaptop, FaMobileAlt, FaDesktop } from 'react-icons/fa';
import { useCasting } from '../../providers/casting-manager';
import { motion } from 'framer-motion';

interface CastDeviceSelectorProps {
  onDeviceSelected: (selected: boolean) => void;
  isOpen: boolean;
  onClose: () => void;
}

export default function CastDeviceSelector({ onDeviceSelected, isOpen, onClose }: CastDeviceSelectorProps) {
  const { 
    isAvailable, 
    isConnected, 
    availableDevices, 
    selectedDevice, 
    connectToDevice, 
    disconnectFromDevice 
  } = useCasting();
  
  const [isScanning, setIsScanning] = useState(false);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [connectionInProgress, setConnectionInProgress] = useState(false);

  // Update the selected device when the connection changes
  useEffect(() => {
    if (selectedDevice) {
      setSelectedDeviceId(selectedDevice.id);
    } else {
      setSelectedDeviceId(null);
    }
  }, [selectedDevice]);

  const handleScanForDevices = () => {
    setIsScanning(true);
    // In a real implementation, this would trigger a rescan
    // The casting-manager handles the actual device detection
    
    // Simulate scan completion after 2 seconds
    setTimeout(() => {
      setIsScanning(false);
    }, 2000);
  };

  const handleDeviceSelect = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
  };

  const handleConnectToDevice = async () => {
    if (!selectedDeviceId) return;
    
    setConnectionInProgress(true);
    
    try {
      const success = await connectToDevice(selectedDeviceId);
      if (success) {
        onDeviceSelected(true);
        onClose();
      }
    } catch (error) {
      console.error('Connection error:', error);
    } finally {
      setConnectionInProgress(false);
    }
  };

  const handleDisconnect = () => {
    disconnectFromDevice();
    setSelectedDeviceId(null);
  };

  // Get icon based on device name
  const getDeviceIcon = (name: string) => {
    const nameLower = name.toLowerCase();
    if (nameLower.includes('tv')) return <FaTv />;
    if (nameLower.includes('laptop')) return <FaLaptop />;
    if (nameLower.includes('phone') || nameLower.includes('mobile')) return <FaMobileAlt />;
    if (nameLower.includes('desktop') || nameLower.includes('pc')) return <FaDesktop />;
    return <FaChromecast />;
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="md"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          Cast to Device
        </ModalHeader>
        <ModalBody>
          {!isAvailable ? (
            <div className="text-center py-4">
              <FaChromecast size={40} className="mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">Casting is not available on this device.</p>
              <p className="text-sm text-gray-500 mt-2">
                Make sure you're using Chrome browser and have enabled casting.
              </p>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Available Devices</h3>
                <Button
                  size="sm"
                  variant="flat"
                  color="primary"
                  isLoading={isScanning}
                  onClick={handleScanForDevices}
                >
                  {isScanning ? 'Scanning...' : 'Scan'}
                </Button>
              </div>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {availableDevices.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    {isScanning ? (
                      <p>Scanning for devices...</p>
                    ) : (
                      <>
                        <p>No casting devices found</p>
                        <p className="text-sm mt-2">Make sure your devices are on the same network</p>
                      </>
                    )}
                  </div>
                ) : (
                  availableDevices.map((device) => (
                    <motion.div
                      key={device.id}
                      whileHover={{ scale: 1.02 }}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedDeviceId === device.id 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                          : 'border-gray-200 dark:border-gray-700'
                      }`}
                      onClick={() => handleDeviceSelect(device.id)}
                    >
                      <div className={`p-2 rounded-full mr-3 ${
                        selectedDeviceId === device.id 
                          ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' 
                          : 'bg-gray-100 text-gray-500 dark:bg-gray-800'
                      }`}>
                        {getDeviceIcon(device.name)}
                      </div>
                      <div>
                        <p className="font-medium">{device.name}</p>
                        {selectedDevice?.id === device.id && (
                          <p className="text-xs text-blue-600 dark:text-blue-400">Connected</p>
                        )}
                      </div>
                    </motion.div>
                  ))
                )}
              </div>
            </>
          )}
        </ModalBody>
        <ModalFooter>
          {isConnected && selectedDevice ? (
            <Button 
              color="danger" 
              variant="flat" 
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          ) : (
            <Button 
              color="primary"
              isDisabled={!selectedDeviceId || !isAvailable}
              isLoading={connectionInProgress}
              onClick={handleConnectToDevice}
            >
              Connect
            </Button>
          )}
          <Button color="default" variant="flat" onClick={onClose}>
            Cancel
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
