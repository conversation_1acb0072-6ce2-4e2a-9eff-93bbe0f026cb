"use client";

import React, { useEffect } from "react";
import { Button } from "@nextui-org/button";
import { useCasting } from "../../providers/casting-manager";
import { useActiveVideo } from "../../providers/active-video-provider";
import { BiCast } from "react-icons/bi";
import { toast } from "react-toastify";

interface CastButtonProps {
  videoId: string;
  className?: string;
}

export default function CastButton({ videoId, className = "" }: CastButtonProps) {
  const { isAvailable, isCasting, castMedia, stopCasting } = useCasting();
  const { currentTime, endTime } = useActiveVideo();

  const handleCastClick = async () => {
    try {
      if (isCasting) {
        stopCasting();
        toast.info("Stopped casting");
      } else {
        if (!videoId) {
          toast.error("No video selected to cast");
          return;
        }
        
        castMedia({
          title: "Power Hour Video",
          url: `https://www.youtube.com/watch?v=${videoId}`,
          mimeType: "video/mp4",
          startTime: currentTime,
          endTime: endTime
        });
        toast.success("Started casting to device");
      }
    } catch (error: any) {
      toast.error(`Cast error: ${error.message || "Unknown error"}`);
    }
  };

  if (!isAvailable) {
    return null; // Don't show button if casting isn't available
  }

  return (
    <Button
      onClick={handleCastClick}
      variant="ghost"
      color="secondary"
      className={`w-10 h-10 ${className} ${isCasting ? "bg-secondary text-white" : ""}`}
      title={isCasting ? "Stop casting" : "Cast to device"}
    >
      <BiCast size={20} />
    </Button>
  );
}
