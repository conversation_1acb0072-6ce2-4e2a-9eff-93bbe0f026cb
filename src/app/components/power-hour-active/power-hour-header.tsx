"use client";
import React from 'react';
import { motion } from "framer-motion";

interface PowerHourHeaderProps {
  title: string;
  currentIndex: number;
  totalSongs: number;
  castingMode: boolean;
}

const PowerHourHeader: React.FC<PowerHourHeaderProps> = ({ 
  title = "Power Hour", 
  currentIndex, 
  totalSongs = 0, 
  castingMode = false 
}) => {
  return (
    <div className="flex flex-col justify-center items-center h-[100vh] bg-gray-900 p-4 w-full">
      <motion.h1
        className="text-2xl md:text-3xl font-bold text-white text-center"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        {title || "Power Hour"}
      </motion.h1>

      <motion.div
        className="flex justify-center items-center gap-2 text-gray-400 text-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <span>
          Song {currentIndex + 1} of {totalSongs}
        </span>
        {castingMode && (
          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-200 text-orange-800">
            Casting
          </span>
        )}
      </motion.div>
    </div>
  );
};

export default PowerHourHeader;
