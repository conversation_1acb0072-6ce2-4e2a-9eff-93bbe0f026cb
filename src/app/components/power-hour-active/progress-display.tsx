import React, { useContext, useEffect, useState } from "react";
import { Progress } from "@nextui-org/react";
import { motion, AnimatePresence } from "framer-motion";
import { ActiveVideoContext } from "../../providers/active-video-provider";
import { ShotGlassIcon } from "./shot-glass-icon";

// Declare JSX namespace to fix the 'JSX element implicitly has type any' errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}

const variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const progressVariants = {
  initial: { width: 0 },
  animate: (width: number) => ({ width: `${width}%` }),
};

const timeVariants = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 },
};

interface ProgressTrackerProps {
  count: number;
  total?: number | undefined;
  progressPercent?: number;
}

export function ProgressTracker({ count, total, progressPercent }: ProgressTrackerProps) {
  const { currentTime, startTime, endTime } = useContext(ActiveVideoContext);
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [overallProgress, setOverallProgress] = useState(progressPercent || 0);
  
  // Ensure count and total are valid numbers
  const safeCount = isNaN(count) ? 0 : count;
  const safeTotal = total && !isNaN(total) ? total : 60; // Default to 60 if not provided

  useEffect(() => {
    // Handle NaN or negative values
    const elapsed = !isNaN(currentTime) && !isNaN(startTime) ? currentTime - startTime : 0;
    setProgress(Math.max(0, elapsed));
    setTimeLeft(Math.max(0, 60 - elapsed));
    
    // Update overall progress if provided
    if (progressPercent !== undefined) {
      setOverallProgress(progressPercent);
    }
  }, [currentTime, startTime, progressPercent]);

  const progressPercentage = (progress / 60) * 100;

  // Format the remaining time as MM:SS
  const formatTime = (seconds: number): string => {
    // Handle NaN, undefined or negative values
    if (isNaN(seconds) || seconds === undefined || seconds < 0) {
      return "0:00";
    }
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  return (
    <motion.div
      className="w-full flex flex-col gap-2"
      initial="hidden"
      animate="visible"
      variants={variants}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      {/* Song Progress Timeline */}
      <div className="flex justify-between items-center mb-1 text-sm text-gray-400">
        <span className="text-white">Current Song</span>
        <AnimatePresence mode="wait">
          <motion.span
            key={timeLeft}
            className={`text-sm font-medium ${
              timeLeft < 10 ? "text-red-400" : "text-gray-300"
            }`}
            initial="initial"
            animate="animate"
            exit="exit"
            variants={timeVariants}
            transition={{ type: "spring", stiffness: 200, damping: 15 }}
          >
            {formatTime(timeLeft)} left
          </motion.span>
        </AnimatePresence>
      </div>
      
      {/* Overall song progress indicator */}
      <div className="flex justify-between items-center text-xs text-gray-400 mb-1">
        <span>{safeCount + 1} of {safeTotal}</span>
        <span>{Math.round(overallProgress)}% Complete</span>
      </div>
      
      {/* Overall Power Hour Progress */}
      <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-2">
        <motion.div
          className="h-full bg-green-500"
          initial={{ width: 0 }}
          animate={{ width: `${overallProgress}%` }}
          transition={{ type: "spring", damping: 15 }}
        />
      </div>

      {/* Custom Progress Bar */}
      <div className="w-full h-3 bg-gray-700 rounded-full overflow-hidden relative">
        <motion.div
          className={`h-full ${
            progressPercentage > 75
              ? "bg-red-500"
              : progressPercentage > 50
              ? "bg-orange-500"
              : "bg-blue-500"
          }`}
          initial="initial"
          animate="animate"
          custom={progressPercentage}
          variants={progressVariants}
          transition={{ type: "spring", damping: 15 }}
        />

        {/* Progress markers for each 15 seconds */}
        {[25, 50, 75].map((marker) => (
          <div
            key={marker}
            className={`absolute top-0 bottom-0 w-0.5 bg-gray-600 ${
              progressPercentage >= marker ? "opacity-0" : "opacity-100"
            }`}
            style={{ left: `${marker}%` }}
          />
        ))}
      </div>
    </motion.div>
  );
}
