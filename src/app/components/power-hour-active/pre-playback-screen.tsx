"use client";
import { useState, useEffect } from "react";
import { Button, useDisclosure } from "@nextui-org/react";
import { PowerHour, PowerHourEntry } from "../../../models/power-hour";
import { motion } from "framer-motion";
import { FaPlay, FaChromecast, FaDesktop, FaTablet, FaCog } from "react-icons/fa";
import CastDeviceSelector from "./cast-device-selector";
import { useCasting } from "../../providers/casting-manager";

interface PrePlaybackScreenProps {
  powerHour: PowerHour | null;
  onStartPlayback: () => void;
  onStartCasting: () => void;
  onStartTabCasting: () => void;
  onStartDesktopCasting: () => void;
}

// Add JSX namespace to fix implicit any errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}

export default function PrePlaybackScreen({
  powerHour,
  onStartPlayback,
  onStartCasting,
  onStartTabCasting,
  onStartDesktopCasting,
}: PrePlaybackScreenProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isAvailable, isConnected, selectedDevice, castTab, castDesktop, castingMode } = useCasting();
  
  // Get the first 4 entries to display as a preview
  const previewEntries = powerHour?.entries?.slice(0, 4) || [];
  
  // Calculate total duration
  const totalDuration = powerHour?.entries?.length ? powerHour.entries.length * 60 : 0; // 60 seconds per song
  const hours = Math.floor(totalDuration / 3600);
  const minutes = Math.floor((totalDuration % 3600) / 60);
  
  const handlePlayClick = () => {
    setIsLoading(true);
    onStartPlayback();
  };
  
  const handleCastClick = () => {
    if (isConnected && selectedDevice) {
      // If we're already connected, start casting immediately
      setIsLoading(true);
      onStartCasting();
    } else {
      // Otherwise open the device selector
      onOpen();
    }
  };
  
  // Handler for tab casting option
  const handleTabCastClick = () => {
    setIsLoading(true);
    castTab();
    onStartCasting();
  };
  
  // Handler for desktop casting option
  const handleDesktopCastClick = () => {
    setIsLoading(true);
    castDesktop();
    onStartCasting();
  };
  
  // Callback when a device is selected from the modal
  const handleDeviceSelected = (selected: boolean) => {
    if (selected) {
      setIsLoading(true);
      onStartCasting();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 bg-gray-900 text-white">
      {/* Cast Device Selector Modal */}
      <CastDeviceSelector 
        isOpen={isOpen} 
        onClose={onClose} 
        onDeviceSelected={handleDeviceSelected} 
      />
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl bg-gray-800 rounded-xl shadow-2xl overflow-hidden"
      >
        <div className="p-6">
          <h1 className="text-3xl font-bold mb-1">{powerHour?.title || "Power Hour"}</h1>
          <p className="text-gray-400 mb-4">
            {powerHour?.entries?.length || 0} songs • {hours > 0 ? `${hours}h ` : ''}{minutes}m
          </p>
          
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-3">Ready to start your power hour?</h2>
            <p className="text-gray-300">
              Choose how you want to enjoy this power hour experience:
            </p>
          </div>
          
          <div className="flex flex-col gap-4 mb-8">
            {/* Main action buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handlePlayClick}
                disabled={isLoading}
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-6 flex-1"
                startContent={<FaPlay />}
              >
                Play on this device
              </Button>
              
              {isConnected && selectedDevice ? (
                <Button
                  onClick={handleCastClick}
                  disabled={isLoading}
                  size="lg"
                  className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-6 flex-1 group"
                  startContent={
                    <div className="flex items-center">
                      <FaChromecast className="mr-2" />
                      <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse mr-2"></div>
                    </div>
                  }
                >
                  <div className="flex flex-col items-start text-left">
                    <span>Cast to {selectedDevice.name}</span>
                    <span className="text-xs opacity-80 group-hover:opacity-100 transition-opacity">Connected</span>
                  </div>
                </Button>
              ) : (
                <Button
                  onClick={handleCastClick}
                  disabled={isLoading || (!isConnected && !isAvailable)}
                  size="lg"
                  className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-6 flex-1"
                  startContent={<FaChromecast />}
                >
                  Cast to TV/device
                  {!isAvailable && <span className="text-xs ml-2">(Not available)</span>}
                </Button>
              )}
            </div>
            
            {/* Straightforward casting options */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={onStartTabCasting}
                disabled={isLoading || !isAvailable}
                size="md"
                className="bg-purple-600 hover:bg-purple-700 text-white flex-1"
                startContent={<FaTablet />}
              >
                Cast current tab
                <span className="text-xs ml-2">(Recommended)</span>
              </Button>
              
              <Button
                onClick={onStartDesktopCasting}
                disabled={isLoading || !isAvailable}
                size="md"
                className="bg-indigo-600 hover:bg-indigo-700 text-white flex-1"
                startContent={<FaDesktop />}
              >
                Cast desktop window
              </Button>
            </div>
          </div>
          
          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-2">Preparing your power hour...</span>
            </div>
          )}
        </div>
        
        {/* Preview of songs */}
        {previewEntries.length > 0 && (
          <div className="px-6 pb-6">
            <h3 className="text-lg font-semibold mb-3">Preview:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {previewEntries.map((entry: PowerHourEntry, index: number) => (
                <div key={entry.id || index} className="flex items-center p-3 bg-gray-700 rounded-lg">
                  <div className="w-12 h-12 flex-shrink-0 mr-3 bg-gray-600 rounded overflow-hidden">
                    {(entry.video as any)?.thumbnail || entry.song?.thumbnailUrl ? (
                      <img 
                        src={(entry.video as any)?.thumbnail || entry.song?.thumbnailUrl} 
                        alt="Song thumbnail" 
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-thumbnail.jpg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-600">
                        <span className="text-gray-400 text-xs">No img</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {(entry.song as any)?.name || entry.song?.title || 'Unknown Song'}
                    </p>
                    <p className="text-xs text-gray-400 truncate">
                      {entry.song?.artist || 'Unknown Artist'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            {powerHour?.entries && powerHour.entries.length > 4 && (
              <p className="text-sm text-gray-400 text-center mt-3">
                +{powerHour.entries.length - 4} more songs
              </p>
            )}
          </div>
        )}
      </motion.div>
    </div>
  );
}
