"use client";
import { PlayButton } from "./play-button";
import CastButton from "./cast-button";

import { <PERSON><PERSON>, Toolt<PERSON> } from "@nextui-org/react";
import { ActiveVideoContext, useActiveVideo } from "../../providers/active-video-provider";
import { useContext, useEffect } from "react";
import { motion } from "framer-motion";
import { FaStepForward, FaStepBackward, FaRandom } from "react-icons/fa";
import { IoMdSkipForward } from "react-icons/io";

// Animation variants for buttons
const buttonVariants = {
	initial: { scale: 0, opacity: 0 },
	animate: (custom: number) => ({
		scale: 1,
		opacity: 1,
		transition: {
			type: "spring",
			stiffness: 260,
			damping: 20,
			delay: custom * 0.1,
		},
	}),
	hover: { scale: 1.05, transition: { duration: 0.2 } },
	tap: { scale: 0.95, transition: { duration: 0.1 } },
	disabled: { opacity: 0.5, scale: 0.95 },
};

export default function YoutubeControls({
	powerHourStarted = false,
	canGoPrevious,
	canGoNext,
	onPreviousClick,
	onNextClick,
	videoId,
	castingMode = false,
}: {
	powerHourStarted?: boolean;
	canGoPrevious?: boolean;
	canGoNext?: boolean;
	onPreviousClick: () => void;
	onNextClick: () => void;
	videoId: string;
	castingMode?: boolean;
}) {
	const { playing, askToToggle } = useActiveVideo();

	return (
		<motion.div 
			className="flex flex-col items-center w-full"
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.4 }}
		>
			{/* Main Controls */}
			<div className="flex items-center justify-center gap-x-3 sm:gap-x-6 w-full mb-3 relative">
				{/* Casting Status Indicator */}
				{castingMode && (
					<motion.div
						className="absolute -top-8 left-1/2 transform -translate-x-1/2"
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.5 }}
					>
						<span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-200 text-orange-800 shadow-md">
							<span className="w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse"></span>
							Casting Active
						</span>
					</motion.div>
				)}
				
				{/* Previous Button */}
				<Tooltip content={canGoPrevious ? "Previous Song" : "No Previous Songs"} placement="top">
					<div>
						<motion.button
							disabled={!canGoPrevious}
							onClick={onPreviousClick}
							className={`w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center ${canGoPrevious ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-800 opacity-40'} border border-gray-700 shadow-lg transition-all`}
							variants={buttonVariants}
							custom={0}
							initial="initial"
							animate="animate"
							whileHover={canGoPrevious ? "hover" : "disabled"}
							whileTap={canGoPrevious ? "tap" : "disabled"}
						>
							<FaStepBackward className={`text-xl ${canGoPrevious ? 'text-blue-400' : 'text-gray-500'}`} />
						</motion.button>
					</div>
				</Tooltip>

				{/* Play/Pause Button */}
				<motion.div
					variants={buttonVariants}
					custom={1}
					initial="initial"
					animate="animate"
					whileHover="hover"
					whileTap="tap"
					className="z-10"
				>
					<PlayButton
						playing={playing}
						onToggle={askToToggle}
						color={castingMode ? "warning" : "primary"}
						className="w-20 h-20 sm:w-24 sm:h-24 shadow-xl"
					/>
				</motion.div>

				{/* Next Button */}
				<Tooltip content={canGoNext ? "Next Song" : "End of Power Hour"} placement="top">
					<div>
						<motion.button
							disabled={!canGoNext}
							onClick={onNextClick}
							className={`w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center ${canGoNext ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-800 opacity-40'} border border-gray-700 shadow-lg transition-all`}
							variants={buttonVariants}
							custom={2}
							initial="initial"
							animate="animate"
							whileHover={canGoNext ? "hover" : "disabled"}
							whileTap={canGoNext ? "tap" : "disabled"}
						>
							<FaStepForward className={`text-xl ${canGoNext ? 'text-blue-400' : 'text-gray-500'}`} />
						</motion.button>
					</div>
				</Tooltip>
				
				{/* Cast Button or Cast Status */}
				{!castingMode ? (
					<motion.div
						variants={buttonVariants}
						custom={3}
						initial="initial"
						animate="animate"
					>
						<CastButton videoId={videoId} className="w-12 h-12 sm:w-14 sm:h-14" />
					</motion.div>
				) : (
					<Tooltip content="Currently Casting" placement="top">
						<motion.div
							variants={buttonVariants}
							custom={3}
							initial="initial"
							animate="animate"
						>
							<div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center bg-orange-600 shadow-lg border border-orange-700">
								<span className="text-xs text-white">LIVE</span>
							</div>
						</motion.div>
					</Tooltip>
				)}
			</div>
			
			{/* Skip Forward 10 seconds button */}
			<motion.div 
				className="flex justify-center mt-1"
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ delay: 0.6 }}
			>
				<Tooltip content="Skip 10 seconds" placement="bottom">
					<button 
						className="flex items-center gap-1 py-1 px-3 rounded-full bg-gray-800 hover:bg-gray-700 text-xs text-gray-300 transition-all"
						onClick={() => {
							// Skip forward 10 seconds - this would need actual implementation
							console.log('Skip 10s');
						}}
					>
						<IoMdSkipForward className="text-blue-400" />
						10s
					</button>
				</Tooltip>
			</motion.div>
		</motion.div>
	);
}
