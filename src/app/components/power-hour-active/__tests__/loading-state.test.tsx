import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import LoadingState from '../loading-state';

describe('LoadingState Component', () => {
  it('renders the loading spinner and default message', () => {
    const { getByRole, getByText } = render(<LoadingState />);
    
    // Check if the loading spinner is present
    const spinner = getByRole('img', { hidden: true });
    expect(spinner).toBeInTheDocument();
    
    // Check if the default loading message is displayed
    expect(getByText('Fetching your songs and getting everything ready...')).toBeInTheDocument();
  });
  
  it('renders custom loading message when provided', () => {
    const customMessage = 'Custom loading message';
    const { getByRole, getByText, queryByText } = render(<LoadingState message={customMessage} />);
    
    // Check if the loading spinner is present
    const spinner = getByRole('img', { hidden: true });
    expect(spinner).toBeInTheDocument();
    
    // Check if the custom loading message is displayed
    expect(getByText(customMessage)).toBeInTheDocument();
    
    // Ensure the default message is not displayed
    expect(queryByText('Fetching your songs and getting everything ready...')).not.toBeInTheDocument();
  });
});
