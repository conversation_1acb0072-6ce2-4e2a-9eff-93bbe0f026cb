import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import ErrorState from '../error-state';

// Mock the Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn().mockImplementation(() => ({
    push: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  })),
}));

describe('ErrorState Component', () => {
  it('renders the error icon and default message', () => {
    const { getByTestId, getByText } = render(<ErrorState hasId={false} />);
    
    // Check if the error icon is present
    expect(getByTestId('error-icon')).toBeInTheDocument();
    
    // Check if the default error message is displayed
    expect(getByText('Unable to Load Power Hour')).toBeInTheDocument();
  });
  
  it('renders custom error message when provided', () => {
    const customMessage = 'Custom error message';
    const { getByTestId, getByText, queryByText } = render(
      <ErrorState hasId={true} message={customMessage} />
    );
    
    // Check if the error icon is present
    expect(getByTestId('error-icon')).toBeInTheDocument();
    
    // Check if the custom error message is displayed
    expect(getByText(customMessage)).toBeInTheDocument();
    
    // Ensure the default message is not displayed
    expect(queryByText('No Power Hour ID was provided')).not.toBeInTheDocument();
  });
});
