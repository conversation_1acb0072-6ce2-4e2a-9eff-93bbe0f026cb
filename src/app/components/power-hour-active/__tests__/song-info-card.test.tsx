import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import SongInfoCard from '../song-info-card';

describe('SongInfoCard Component', () => {
  it('renders the song information correctly', () => {
    const { getByText } = render(
      <SongInfoCard 
        title="Test Song" 
        artist="Test Artist" 
        year={2023} 
      />
    );
    
    // Check if the song title is displayed
    expect(getByText('Test Song')).toBeInTheDocument();
    
    // Check if the artist is displayed
    expect(getByText('Test Artist')).toBeInTheDocument();
    
    // Check if the year is displayed
    expect(getByText('Year: 2023')).toBeInTheDocument();
  });
  
  it('handles missing year data gracefully', () => {
    const { getByText, queryByText } = render(
      <SongInfoCard 
        title="Incomplete Song" 
        artist="Some Artist" 
      />
    );
    
    // Check if the song title is displayed
    expect(getByText('Incomplete Song')).toBeInTheDocument();
    
    // Check if the artist is displayed
    expect(getByText('Some Artist')).toBeInTheDocument();
    
    // Check that missing fields don't cause issues
    expect(queryByText(/Year:/)).not.toBeInTheDocument();
  });
  
  it('uses default values when necessary', () => {
    const { getByText } = render(<SongInfoCard title="" artist="" />);
    
    // Check if default values are used
    expect(getByText('Unknown Song')).toBeInTheDocument();
    expect(getByText('Unknown Artist')).toBeInTheDocument();
  });
});
