import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import PowerHourHeader from '../power-hour-header';

describe('PowerHourHeader Component', () => {
  const mockProps = {
    title: 'Test Power Hour',
    currentIndex: 2,
    totalSongs: 10,
    castingMode: false
  };

  it('renders the title and song counter correctly', () => {
    const { getByText } = render(
      <PowerHourHeader 
        title={mockProps.title}
        currentIndex={mockProps.currentIndex}
        totalSongs={mockProps.totalSongs}
        castingMode={mockProps.castingMode}
      />
    );
    
    // Check if the title is displayed
    expect(getByText(mockProps.title)).toBeInTheDocument();
    
    // Check if the song counter is displayed correctly
    expect(getByText(`Song ${mockProps.currentIndex + 1} of ${mockProps.totalSongs}`)).toBeInTheDocument();
  });
  
  it('handles empty title properly', () => {
    const { getByText } = render(
      <PowerHourHeader 
        title=""
        currentIndex={mockProps.currentIndex}
        totalSongs={mockProps.totalSongs}
        castingMode={mockProps.castingMode}
      />
    );
    
    // Check if the default title is displayed
    expect(getByText('Power Hour')).toBeInTheDocument();
  });
  
  it('shows correct song count at the beginning', () => {
    const { getByText } = render(
      <PowerHourHeader 
        title={mockProps.title}
        currentIndex={0}
        totalSongs={mockProps.totalSongs}
        castingMode={mockProps.castingMode}
      />
    );
    
    // Check if the song counter shows "Song 1 of 10"
    expect(getByText(`Song 1 of ${mockProps.totalSongs}`)).toBeInTheDocument();
  });
});
