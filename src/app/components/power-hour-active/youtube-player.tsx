"use client";

import React, { useEffect, useRef } from "react";
import YouTube, {
  YouTubeProps,
  YouTubePlayer,
  YouTubeEvent,
} from "react-youtube";
import { useActiveVideo } from "../../providers/active-video-provider";
import { toast } from "react-toastify";

// Declare JSX namespace to fix the 'JSX element implicitly has type any' errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}

// Define YouTube event types
interface YouTubePlayerEvent {
  target: YouTubePlayer;
  data?: any;
}

interface YouTubePlayerProps {
  videoId: string;
  startTime?: number;
  endTime?: number;
  onSongEnd?: (dueToError?: boolean) => void;
  onSongError?: () => void;
  castingMode?: boolean; // Added to control behavior when casting
}

export default function YoutubePlayer({
  videoId,
  startTime = 0,
  endTime = 60,
  onSongEnd,
  onSongError,
  castingMode = false,
}: YouTubePlayerProps) {
  // const playerRef = useRef<YouTubePlayer | null>(null);

  const { playing, playerRef, setPlaying, setCurrentTime, setPlayerIsReady } =
    useActiveVideo();

  const opts: YouTubeProps["opts"] = {
    height: "100%",
    width: "100%",
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 1,
      controls: 0,
      disablekb: 1,
      modestbranding: 1,
      rel: 0,
    },
  };

  // Track player ready state with a local ref to avoid the error
  const isPlayerInitialized = useRef(false);

  useEffect(() => {
    // Only attempt to control playback if player is initialized and not in casting mode
    if (playerRef.current && isPlayerInitialized.current && !castingMode) {
      try {
        // Use a safe approach with optional chaining
        playerRef.current?.playVideo?.();
      } catch (error) {
        console.error("Error controlling player:", error);
      }
    }
  }, [castingMode]); // Only depend on castingMode, not the ref itself

  useEffect(() => {
    if (!videoId) {
      console.warn("No video ID provided to YouTube player");
      return;
    }

    // Safety check to make sure we have a valid player reference
    if (playerRef.current) {
      try {
        // Try to load the video
        playerRef?.current?.loadVideoById(videoId, startTime, endTime);
        setCurrentTime(startTime);
      } catch (error) {
        console.error("Error loading video:", videoId, error);
        toast.error(`Error loading video: ${videoId}`);
        // Notify parent component about the error
        if (onSongError) onSongError();
      }
    }
  }, [videoId, startTime, endTime]);

  useEffect(() => {
    if (playerRef.current) {
      setCurrentTime(Math.floor(playerRef.current.getCurrentTime()));
    }
  }, [playerRef.current]);

  const onPlayerReady: YouTubeProps["onReady"] = (
    event: YouTubePlayerEvent
  ) => {
    // access to player in all event handlers via event.target
    playerRef.current = event.target;
    // Set both the global player ready state and our local initialization flag
    setPlayerIsReady(true);
    isPlayerInitialized.current = true;
  };

  const onPlay: YouTubeProps["onPlay"] = (event: YouTubePlayerEvent) => {
    setPlaying(true);
    console.log("Successfully started playing");
  };

  const onPause: YouTubeProps["onPause"] = (event: YouTubePlayerEvent) => {
    setPlaying(false);
    console.log("Successfully paused");
  };

  const onEnd: YouTubeProps["onEnd"] = (event: YouTubePlayerEvent) => {
    if (onSongEnd) {
      onSongEnd(false);
    }
  };

  const onStateChange: YouTubeProps["onStateChange"] = (
    event: YouTubePlayerEvent
  ) => {
    console.debug("onStateChange", event.data);
  };

  const onError: YouTubeProps["onError"] = (event: YouTubePlayerEvent) => {
    if (onSongError) {
      onSongError();
    }
    // Also call onSongEnd with error flag if it exists
    if (onSongEnd) {
      onSongEnd(true);
    }
  };

  // const onAutoPlayBlocked: YouTubeProps["onError"] = (event: YouTubePlayerEvent) => {
  // 	toast.error("Auto play blocked");
  // };

  const currentTimeChanged = (event: YouTubePlayerEvent) => {
    setCurrentTime(event.target.getCurrentTime());
  };

  return (
    <div
      className="bg-900"
      style={{
        border: castingMode ? "8px solid orange" : "8px solid black",
        borderRadius: "4px",
        height: "100%",
        width: "100%",
        boxSizing: "border-box",
        padding: "8px",
      }}
    >
      {castingMode ? (
        <div className="flex items-center justify-center h-full w-full bg-gray-800 text-white">
          <div className="text-center p-6">
            <div className="mb-4 text-5xl text-orange-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M2.35 19.65A1 1 0 0 1 1 18.5V15a1 1 0 0 1 2 0v3.5c0 .55-.45 1-1 1s-1-.45-1-1V15a3 3 0 1 1 6 0v4.5" />
                <path d="M5.35 19A2 2 0 0 1 3.35 17v-2a4 4 0 1 1 8 0V17a2 2 0 0 1-2 2zm9-5A9 9 0 0 0 8.35 6" />
                <path d="M20.35 14v-2a12 12 0 0 0-12-12" />
                <path d="M17.35 14v-2a9 9 0 0 0-9-9" />
                <path d="M14.35 14v-2a6 6 0 0 0-6-6" />
              </svg>
            </div>
            <h3 className="text-xl mb-2">Casting to External Device</h3>
            <p className="text-gray-300 mb-2">Now playing: {videoId}</p>
            <p className="text-gray-400 text-sm">
              Control playback using the buttons below
            </p>
          </div>
        </div>
      ) : (
        <YouTube
          style={{ height: "100%", width: "100%" }}
          videoId={videoId}
          opts={{
            ...opts,
            playerVars: {
              ...opts.playerVars,
              autoplay: playing ? 1 : 0,
              start: startTime,
              end: endTime,
            },
          }}
          onReady={onPlayerReady}
          onPlay={onPlay} // defaults -> noop
          onPause={onPause} // defaults -> noop
          onEnd={onEnd} // defaults -> noop
          onError={onError}
          onStateChange={onStateChange}
          // onAutoPlayBlocked={onAutoPlayBlocked}
        />
      )}
    </div>
  );
}
