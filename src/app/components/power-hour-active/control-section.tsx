"use client";
import React from 'react';
import { motion } from "framer-motion";
import YoutubeControls from "./youtube-controls";
import { ProgressTracker } from "./progress-display";
import PHEntryFeedback from "../feed-back/ph-entry-feedback-controls";

interface ControlSectionProps {
  canGoPrevious: boolean;
  canGoNext: boolean;
  onPreviousClick: () => void;
  onNextClick: () => void;
  videoId: string;
  castingMode: boolean;
  currentIndex: number;
  totalEntries?: number;
  powerHourId?: string;
  entryId?: string;
}

const ControlSection: React.FC<ControlSectionProps> = ({
  canGoPrevious,
  canGoNext,
  onPreviousClick,
  onNextClick,
  videoId,
  castingMode,
  currentIndex,
  totalEntries = 0,
  powerHourId,
  entryId,
}) => {
  const progressPercent = totalEntries ? ((currentIndex + 1) / totalEntries) * 100 : 0;

  return (
    <motion.div
      className="pt-4 pb-2 px-4 backdrop-blur-md bg-gray-900/80 border-t border-gray-800 rounded-t-3xl shadow-[0_-15px_40px_-15px_rgba(0,0,0,0.3)]"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ delay: 0.6, type: "spring", stiffness: 100 }}
      style={{ marginTop: "auto" }}
    >
      <YoutubeControls
        canGoPrevious={canGoPrevious}
        canGoNext={canGoNext}
        onPreviousClick={onPreviousClick}
        onNextClick={onNextClick}
        videoId={videoId}
        castingMode={castingMode}
      />

      <ProgressTracker
        count={currentIndex}
        total={totalEntries}
        progressPercent={progressPercent}
      />

      {powerHourId && entryId && (
        <motion.div
          className="mt-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <PHEntryFeedback
            powerHourId={powerHourId}
            powerHourEntryId={entryId}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

export default ControlSection;
