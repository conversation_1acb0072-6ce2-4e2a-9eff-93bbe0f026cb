"use client";
import React from 'react';
import { motion } from "framer-motion";
import { Song } from "@/models/power-hour";

interface SongInfoCardProps {
  title: string;
  artist: string;
  year?: number;
}

const SongInfoCard: React.FC<SongInfoCardProps> = ({ 
  title = "Unknown Song", 
  artist = "Unknown Artist", 
  year 
}) => {
  return (
    <motion.div
      className="mt-6 mb-4 bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 text-center border border-gray-700 shadow-lg"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.4, duration: 0.5 }}
    >
      <h2 className="text-xl md:text-2xl font-bold text-white mb-1">
        {title}
      </h2>
      <p className="text-blue-300 md:text-lg">
        {artist}
      </p>
      {year && (
        <p className="text-gray-400 text-sm mt-1">
          Released: {year}
        </p>
      )}
    </motion.div>
  );
};

export default SongInfoCard;
