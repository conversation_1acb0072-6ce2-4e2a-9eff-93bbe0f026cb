"use client";
import React from 'react';

interface LoadingStateProps {
  message?: string;
}

const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = "Fetching your songs and getting everything ready..." 
}) => {
  return (
    <div className="container mx-auto flex flex-col h-[80vh] justify-center items-center text-center p-8">
      <div className="bg-gray-800 rounded-xl p-10 shadow-xl border border-gray-700 max-w-md w-full">
        <div className="mb-6">
          <svg
            className="animate-spin h-12 w-12 text-blue-500 mx-auto"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white mb-3">
          Loading Your Power Hour
        </h2>
        <p className="text-gray-300">
          {message}
        </p>
      </div>
    </div>
  );
};

export default LoadingState;
