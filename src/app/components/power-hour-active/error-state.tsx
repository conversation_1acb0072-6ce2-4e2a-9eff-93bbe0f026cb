"use client";
import React from 'react';
import { Button } from "@nextui-org/react";
import { useRouter } from "next/navigation";

interface ErrorStateProps {
  message?: string;
  hasId: boolean;
}

const ErrorState: React.FC<ErrorStateProps> = ({ 
  message,
  hasId
}) => {
  const router = useRouter();
  
  return (
    <div className="container mx-auto flex flex-col h-[80vh] justify-center items-center text-center p-8">
      <div className="bg-gray-800 rounded-xl p-10 shadow-xl border border-red-500 max-w-md w-full">
        <div className="mb-6 text-red-500">
          <svg
            data-testid="error-icon"
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white mb-3">
          Unable to Load Power Hour
        </h2>
        <p className="text-gray-300 mb-6">
          {message || (!hasId
            ? "No Power Hour ID was provided"
            : "This Power Hour doesn't have any songs")}
        </p>
        <Button
          color="primary"
          onClick={() => router.push("/")}
          className="mx-auto w-full"
        >
          Return to Home
        </Button>
      </div>
    </div>
  );
};

export default ErrorState;
