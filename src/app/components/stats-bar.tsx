import React from "react";
import { AiOutlineEye } from "react-icons/ai";
import { FaThumbsDown, FaThumbsUp } from "react-icons/fa";

interface StatsBarProps {
	upvotes?: number;
	downvotes?: number;
	views?: number;
}

const StatsBar: React.FC<StatsBarProps> = ({ upvotes, downvotes, views }) => {
	return (
		<div className="flex items-center justify-center space-x-4 bg-gray-900 p-4 w-full">
			<div className="flex items-center space-x-2">
				<FaThumbsUp className="text-blue-500" />
				<span>{upvotes ?? "-"}</span>
			</div>
			<div className="flex items-center space-x-2">
				<FaThumbsDown className="text-red-500" />
				<span>{downvotes ?? "-"}</span>
			</div>
			<div className="flex items-center space-x-2">
				<AiOutlineEye className="text-green-500" />
				<span>{views ?? "-"}</span>
			</div>
		</div>
	);
};

export default StatsBar;
