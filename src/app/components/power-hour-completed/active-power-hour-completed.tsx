import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "@/app/power-hour-ai/loading";
import * as RTDB from "../../providers/power-hour";
import CompletionHeader from "./completion-header";
import SessionFeedback from "../feed-back/ph-feedback-controls";
import { FaCheck, FaTimes } from "react-icons/fa";
import { PowerHour } from "@/models/power-hour";
import PHEntryFeedbackCard from "../feed-back/ph-entry-feedback-card";

const ActivePowerHourCompleted = () => {
	const [powerHour, setPowerHour] = useState<PowerHour>();
	const [isLoading, setIsLoading] = useState(true);

	const router = useRouter();
	const searchParams = useSearchParams();
	useEffect(() => {
		const powerHourId = searchParams.get("powerHourId");
		if (powerHourId) {
			RTDB.fetchPowerHour(powerHourId).then((pH) => {
				if (pH) {
					setPowerHour(pH);
				}
				setIsLoading(false);
			});
		}
	}, []);

	return (
		<div className="flex flex-col h-full items-center bg-gray-800 text-white overflow-hidden">
			<CompletionHeader />

			{isLoading ? (
				<Loading />
			) : powerHour && powerHour.entries.length > 0 ? (
				<div className="overflow-auto flex-grow w-full p-4">
					<div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-4">
						{powerHour.entries.map((entry, index) => (
							<PHEntryFeedbackCard
								key={entry.id || `${entry.song.id}-${entry.video.id}` || `entry-${index}`}
								powerHourId={powerHour.id}
								entry={entry}
							/>
						))}
					</div>
				</div>
			) : (
				<div className="w-full h-full flex items-center justify-center">
					No songs available.
				</div>
			)}

			<div className="w-full bg-gray-900 p-4">
				{powerHour && <SessionFeedback powerHourId={powerHour.id} />}
			</div>
		</div>
	);
};

export default ActivePowerHourCompleted;
