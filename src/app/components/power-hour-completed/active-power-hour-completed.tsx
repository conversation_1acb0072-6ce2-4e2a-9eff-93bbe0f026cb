import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "@/app/power-hour-ai/loading";
import * as RTDB from "../../providers/power-hour";
import CompletionHeader from "./completion-header";
import SessionFeedback from "../feed-back/ph-feedback-controls";
import { FaCheck, FaTimes } from "react-icons/fa";
import { PowerHour } from "@/models/power-hour";
import PHEntryFeedbackCard from "../feed-back/ph-entry-feedback-card";

const ActivePowerHourCompleted = () => {
	const [powerHour, setPowerHour] = useState<PowerHour>();
	const [isLoading, setIsLoading] = useState(true);

	const router = useRouter();
	const searchParams = useSearchParams();
	useEffect(() => {
		const powerHourId = searchParams.get("powerHourId");
		if (powerHourId) {
			RTDB.fetchPowerHour(powerHourId).then((pH) => {
				if (pH) {
					setPowerHour(pH);
				}
				setIsLoading(false);
			});
		}
	}, []);

	return (
		<div className="flex flex-col h-full items-center bg-gray-800 text-white overflow-hidden">
			<CompletionHeader />

			{isLoading ? (
				<Loading />
			) : powerHour && powerHour.entries.length > 0 ? (
				<div className="overflow-auto flex-grow w-full">
					{/* Mobile-first layout with better spacing */}
					<div className="p-4 space-y-4 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:gap-4">
						{powerHour.entries.map((entry, index) => (
							<div
								key={entry.id || `${entry.song.id}-${entry.video.id}` || `entry-${index}`}
								className="w-full"
							>
								<PHEntryFeedbackCard
									powerHourId={powerHour.id}
									entry={entry}
								/>
							</div>
						))}
					</div>

					{/* Power Hour summary info for mobile */}
					<div className="p-4 mt-6 bg-gray-700 mx-4 rounded-lg md:hidden">
						<h3 className="font-bold text-lg mb-2">{powerHour.title}</h3>
						<div className="grid grid-cols-2 gap-4 text-sm text-gray-300">
							<div>
								<span className="font-medium">Songs:</span> {powerHour.entries.length}
							</div>
							<div>
								<span className="font-medium">Genre:</span> {powerHour.mostSimilarGenre}
							</div>
							{powerHour.upvotes !== undefined && (
								<div>
									<span className="font-medium">Upvotes:</span> {powerHour.upvotes}
								</div>
							)}
							{powerHour.downvotes !== undefined && (
								<div>
									<span className="font-medium">Downvotes:</span> {powerHour.downvotes}
								</div>
							)}
						</div>
					</div>
				</div>
			) : (
				<div className="w-full h-full flex items-center justify-center">
					<div className="text-center p-8">
						<h3 className="text-xl font-bold mb-2">No songs available</h3>
						<p className="text-gray-400">This power hour doesn't have any entries yet.</p>
					</div>
				</div>
			)}

			<div className="w-full bg-gray-900 p-4">
				{powerHour && <SessionFeedback powerHourId={powerHour.id} />}
			</div>
		</div>
	);
};

export default ActivePowerHourCompleted;
