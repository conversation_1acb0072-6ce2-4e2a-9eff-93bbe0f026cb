import React from "react";
import { PowerHour } from "../../models/power-hour";
import {
	FaMusic,
	FaVideo,
	FaList,
	FaArrowUp,
	FaArrowDown,
	FaThumbsUp,
	FaThumbsDown,
} from "react-icons/fa"; // Example icons
import useFeedback from "../providers/use-feedback";
import { CircularProgress } from "@nextui-org/react";
import Image from "next/image";

const PowerHourCard = ({
	powerHour,
	onClick,
}: {
	powerHour: PowerHour;
	onClick: () => void;
}) => {
	// No longer using the feedback hook to simplify compatibility with Firestore
	const loading = false;

	return (
		<div
			className="flex flex-col h-full justify-evenly items-center rounded overflow-hidden shadow-lg bg-gray-900 text-white cursor-pointer min-h-64 relative"
			onClick={onClick}
		>
			<div className="flex flex-col flex-0 justify-evenly items-center p-4">
				<div className="font-bold text-xl mb-2 text-center">
					{powerHour.title}
				</div>
			</div>
			{/* Thumbnails container */}
			{powerHour.videos && Array.isArray(powerHour.videos) && powerHour.videos.length > 0 ? (
				<div className="flex-1 grid grid-cols-3 grid-rows-3 gap-0">
					{powerHour.videos.slice(0, 9).map((video, index) => (
						<div key={index}>
							<img
								src={video?.thumbnail || '/placeholder-thumbnail.jpg'}
								alt="Thumbnail"
								className="object-cover h-full w-full"
								onError={(e) => {
									// Fallback for broken images
									e.currentTarget.src = '/placeholder-thumbnail.jpg';
								}}
							/>
						</div>
					))}
				</div>
			) : (
				<div className="flex items-center justify-center flex-1 italic text-xs">
					No Thumbnails Available
				</div>
			)}
			<div className="flex justify-evenly items-center py-2 bg-gray-800 w-full z-10">
				<div className="flex items-center">
					<FaMusic className="mr-2" />
					{Array.isArray(powerHour.songs) ? powerHour.songs.length : 0}
				</div>
				<div className="flex items-center">
					<FaVideo className="mr-2" />
					{Array.isArray(powerHour.videos) && powerHour.videos.length > 0 ? "Yes" : "No"}
				</div>
				<div className="flex items-center">
					<FaList className="mr-2" />
					{Array.isArray(powerHour.entries) && powerHour.entries.length > 0 ? "Yes" : "No"}
				</div>
				<div className="flex items-center">
					<FaThumbsUp className="mr-2" />
					{powerHour.upvotes || 0}
				</div>
				<div className="flex items-center">
					<FaThumbsDown className="mr-2" />
					{powerHour.downvotes || 0}
				</div>
			</div>
		</div>
	);
};

export default PowerHourCard;
