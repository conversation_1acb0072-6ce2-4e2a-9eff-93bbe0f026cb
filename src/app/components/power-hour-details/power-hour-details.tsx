import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loading from "@/app/power-hour-ai/loading";
import PHEntryFeedbackCard from "../feed-back/ph-entry-feedback-card";
import { usePowerHour } from "@/hooks/use-power-hour";
import { Button } from "@nextui-org/react";
import PowerHourEntryCard from "../power-hour-entry-card";

const PowerHourDetails = () => {
  const [powerHourId, setPowerHourId] = useState("");
  const router = useRouter();
  const { powerHour, loading, error } = usePowerHour(powerHourId);

  const searchParams = useSearchParams();
  useEffect(() => {
    const powerHourId = searchParams.get("powerHourId");
    if (powerHourId && powerHourId !== "") setPowerHourId(powerHourId);
  }, []);

  const onStartThisPowerHour = () => {
    router.push(`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`);
  };

  return (
    <div className="flex flex-col h-full items-center bg-gray-800 text-white overflow-hidden">
      {loading && !error ? (
        <Loading />
      ) : powerHour && powerHour.entries?.length > 0 ? (
        <>
          <div className="text-xl uppercase">
            {powerHour?.title}{" "}
            <span className="text-xl uppercase">
              ({powerHour.entries?.length} songs)
            </span>
          </div>

          <div className="overflow-auto flex-grow w-full p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 gap-4">
              {powerHour.entries?.map((entry) => (
                <PowerHourEntryCard entry={entry} key={entry.id} />
              ))}
            </div>
          </div>

          <div className="flex w-full items-center justify-center bg-gray-900 p-4">
            <Button onClick={onStartThisPowerHour}>
              Start This Power Hour
            </Button>
          </div>
        </>
      ) : error === null ? (
        <div className="w-full h-full flex items-center justify-center">
          No details available.
        </div>
      ) : (
        <div>
          <div className="text-xl uppercase">Error fetching power hour</div>
          <div>{error?.message}</div>
        </div>
      )}
    </div>
  );
};

export default PowerHourDetails;
