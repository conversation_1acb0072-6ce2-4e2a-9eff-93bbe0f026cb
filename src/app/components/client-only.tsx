"use client";

import { useEffect, useState } from "react";

export default function ClientOnly({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // On first render, isClient is false, so we don't render anything
  // This prevents hydration errors
  if (!isClient) {
    return null;
  }

  return <>{children}</>;
}
