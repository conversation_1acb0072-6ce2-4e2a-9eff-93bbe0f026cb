import React, { useState } from "react";
import { FaThumbsUp, FaThumbsDown, FaBug } from "react-icons/fa";
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>dal<PERSON><PERSON>, ModalFooter, useDisclosure } from "@nextui-org/react";
import useFeedback from "../../providers/use-feedback";

const commonIssues = [
	"Issues with the Power Hour",
	"Issues with the songs",
	"Issues with the videos",
	"Videos didnt match prompt",
];

const SessionFeedback = ({ powerHourId }: { powerHourId: string }) => {
	const [selectedFeedback, setSelectedFeedback] = useState("");
	const { sessionFeedback, setSessionFeedback } = useFeedback(powerHourId);
	const { isOpen, onOpen, onClose } = useDisclosure();

	const sendFeedback = (type: any, detail = "") => {
		sessionFeedback &&
			setSessionFeedback({ ...sessionFeedback, [type]: detail });

		setSelectedFeedback(selectedFeedback === type ? "" : type);
		console.log(`Session feedback: ${type}`, detail);
		if (type === "bug" && detail) {
			onClose(); // Close the modal if a specific bug was selected
		}
	};

	return (
		<div className="flex items-center justify-center w-full p-4 bg-gray-900 text-white">
			<div className="flex items-center justify-center gap-4">
				<Button
					onClick={() => sendFeedback("like")}
					color={selectedFeedback === "like" ? "success" : "default"}
					endContent={<FaThumbsUp />}
				/>
				<Button
					onClick={() => sendFeedback("dislike")}
					color={selectedFeedback === "dislike" ? "danger" : "default"}
					endContent={<FaThumbsDown />}
				/>
				<Button
					color={selectedFeedback === "bug" ? "warning" : "default"}
					endContent={<FaBug />}
					onClick={onOpen}
				/>
				
				{/* Modal for Issue Reporting */}
				<Modal isOpen={isOpen} onClose={onClose} backdrop="blur" size="sm">
					<ModalContent className="bg-gray-800 text-white">
						<ModalHeader className="flex flex-col gap-1">What was wrong?</ModalHeader>
						<ModalBody>
							<div className="w-full">
								<ul className="space-y-2">
									{commonIssues.map((issue, index) => (
										<li
											key={index}
											className="cursor-pointer p-2 rounded-md hover:bg-gray-700 flex items-center"
											onClick={() => sendFeedback("bug", issue)}
										>
											{issue}
										</li>
									))}
								</ul>
							</div>
						</ModalBody>
						<ModalFooter>
							<Button variant="light" color="default" onPress={onClose}>
								Cancel
							</Button>
						</ModalFooter>
					</ModalContent>
				</Modal>
			</div>
		</div>
	);
};

export default SessionFeedback;
