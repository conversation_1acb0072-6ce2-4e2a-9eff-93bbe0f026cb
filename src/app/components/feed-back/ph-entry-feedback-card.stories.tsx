import React from "react";
import type { <PERSON><PERSON>, <PERSON>Obj, StoryFn } from "@storybook/react";

import PHEntryFeedbackCard from "./ph-entry-feedback-card";
import { NextUIProvider } from "@nextui-org/system";
import PHE from "../../assets/phe";

const meta = {
  title: "PHEntryFeedbackCard",
  component: PHEntryFeedbackCard,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  decorators: [
    (Story: StoryFn) => (
      <NextUIProvider>
        <Story />
      </NextUIProvider>
    ),
  ],
} satisfies Meta<typeof PHEntryFeedbackCard>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    powerHourId: "25",
    entry: PHE,
  },
  render: (props: Story["args"]) => (
    <PHEntryFeedbackCard powerHourId={props.powerHourId} entry={props.entry} />
  ),
};
