import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import SessionFeedback from "./ph-feedback-controls";
import { NextUIProvider } from "@nextui-org/system";

const meta = {
  title: "SessionFeedback",
  component: SessionFeedback,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ["autodocs"],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {},
  decorators: [
    (Story: any) => (
      <NextUIProvider>
        <Story />
      </NextUIProvider>
    ),
  ],
} satisfies Meta<typeof SessionFeedback>;

export default meta;

type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    powerHourId: "25",
  },
  render: (props: Story["args"]) => <SessionFeedback {...props} />,
};

// export const Secondary: Story = {
// 	args: {
// 		label: "Button",
// 	},
// };

// export const Large: Story = {
// 	args: {
// 		size: "large",
// 		label: "Button",
// 	},
// };

// export const Small: Story = {
// 	args: {
// 		size: "small",
// 		label: "Button",
// 	},
// };
