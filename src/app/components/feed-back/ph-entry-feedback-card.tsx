import { PowerHourEntry } from "../../../models/power-hour";
import useFeedback from "../../providers/use-feedback";
import React, { useState, useEffect } from "react";
import { FaCheck, FaTimes } from "react-icons/fa";

const PHEntryFeedbackCard = ({
	powerHourId,
	entry,
}: {
	powerHourId: string;
	entry: PowerHourEntry;
}) => {
	const { upVoteEntry, downVoteEntry } = useFeedback(powerHourId);

	const handleFeedback = (entryId: string, like: boolean) => {
		console.log(`Feedback for ${entryId}: ${like ? "Like" : "Dislike"}`);

	};

	return (
		<div className="relative group cursor-pointer rounded-lg overflow-hidden">
			<div className="p-4 bg-gray-900 rounded-lg h-full text-white">
				<p className="font-bold">{entry.song.title}</p>
				<p>{entry.song.artist}</p>
			</div>
			<div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-evenly opacity-0 group-hover:opacity-100 transition-opacity duration-300">
				<button
					className="rounded-lg hover:bg-green-900 focus:ring focus:ring-green-800 p-4"
					onClick={() => handleFeedback(entry.id, true)}
				>
					<FaCheck className="text-green-500" size="24" />
				</button>
				<button
					className="rounded-lg hover:bg-red-900 focus:ring focus:ring-red-800 p-4"
					onClick={() => handleFeedback(entry.id, false)}
				>
					<FaTimes className="text-red-500" size="24" />
				</button>
			</div>
		</div>
	);
};

export default PHEntryFeedbackCard;
