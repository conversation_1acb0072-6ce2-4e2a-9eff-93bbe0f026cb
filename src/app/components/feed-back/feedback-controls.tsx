import React, { useState } from 'react';
import { FaThumbsUp, FaThumbsDown, FaBug } from 'react-icons/fa';

const FeedbackControls = () => {
  const [feedbackSent, setFeedbackSent] = useState(false);
  const [showBugs, setShowBugs] = useState(false);

  const sendFeedback = (type:any, detail = '') => {
    console.log(`Feedback sent: ${type}`, detail);
    // Here, you would send the feedback to Firebase or your backend.
    setFeedbackSent(true);
  };

  const knownBugs = [
    'Issue with Video',
    'Issue with Song',
    'App is stuck',
    'Something else is broken',
  ];

  return (
    <div className='items-center justify-center pt-4 bg-gray-800' style={{ display: 'flex', flexDirection: 'row', gap: '10px', alignItems: 'center' }}>
      <button onClick={() => sendFeedback('like')} disabled={feedbackSent} title="I like this">
        <FaThumbsUp />
      </button>
      <button onClick={() => sendFeedback('dislike')} disabled={feedbackSent} title="I dislike this">
        <FaThumbsDown />
      </button>
      <button onClick={() => setShowBugs(!showBugs)} disabled={feedbackSent} title="Report a bug">
        <FaBug />
      </button>

      {showBugs && (
        <div style={{ position: 'absolute', backgroundColor: 'white', padding: '10px', border: '1px solid black', marginTop: '5px' }}>
          <p>Known Issues:</p>
          <ul>
            {knownBugs.map((bug, index) => (
              <li key={index} style={{ cursor: 'pointer' }} onClick={() => sendFeedback('bug', bug)}>
                {bug}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FeedbackControls;
