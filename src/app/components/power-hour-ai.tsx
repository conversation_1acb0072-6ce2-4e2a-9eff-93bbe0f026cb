"use client";

import React, { useState, useEffect } from "react";
import {
  PowerHour,
  PowerHourEntry,
  PowerHourID,
  Song,
} from "../../models/power-hour";
import Landing from "../components/landing";
import { PHOperations, PHCreation } from "../../models/enums";
import * as RTDB from "../providers/power-hour";
import { N8nProvider } from "../providers/n8n-provider";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import { PHActions } from "../../../functions/src/[models]/enums";

export default function PowerHourAI() {
  const [search, setSearch] = useState("");
  const [powerHour, setPowerHour] = useState<PowerHour>({} as PowerHour);
  const [count, setCount] = useState(5);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    console.log("reading query params");
    const powerHourId = searchParams.get("powerHourId");
    if (
      powerHourId &&
      powerHourId !== "" &&
      !powerHour.title &&
      !powerHour.songs &&
      !powerHour.entries
    ) {
      console.log("ID in QP, Fetching power hour: ", powerHourId);
      RTDB.fetchPowerHour(powerHour.id).then((pH: PowerHour | null) => {
        if (pH) {
          setPowerHour(pH);
        }
      });
    }
  }, []);

  // Function to handle search and navigation
  const handleSearch = async (searchTerm: string) => {
    if (searchTerm && searchTerm.length !== 0) {
      toast.info("Creating your power hour...");
      console.log("Search Submitted: ", searchTerm, count);

      try {
        console.log("Sending request to n8n...");
        // Send only the search term to n8n and get back the power hour ID
        const result = await N8nProvider.createPowerHour(searchTerm);
        console.log("n8n request completed successfully", result);

        // Check if the result contains an error
        if (result?.error) {
          console.error("n8n returned an error:", result.error);
          toast.error(`Error: ${result.error.error || "Failed to create power hour"}`);
          return;
        }

        if (result?.powerHourId) {
          console.log("Navigating to creation page with ID:", result.powerHourId);
          // Navigate to the power hour page with the ID returned from n8n
          router.push(
            "/power-hour-ai/create-power-hour?powerHourId=" + result.powerHourId
          );
        } else {
          console.error("No power hour ID returned from n8n:", result);
          toast.error("Error: No power hour ID returned from n8n");
        }
      } catch (error) {
        console.error("Error sending request to n8n:", error);
        toast.error("Failed to create power hour. Please try again.");
      }
    }
  };

  // Effect to handle search changes - removed to prevent automatic search on every change
  useEffect(() => {
    // Only process search if coming from a URL parameter
    const searchParam = searchParams.get("search");
    if (searchParam && searchParam !== search) {
      setSearch(searchParam);
      handleSearch(searchParam);
    }
  }, [searchParams]);

  // Function to handle search submission from the Landing component
  const onSearchSubmit = (searchTerm: string) => {
    setSearch(searchTerm);
    handleSearch(searchTerm);
  };

  return (
    <Landing
      search={search}
      setSearch={setSearch}
      count={count}
      setCount={setCount}
      onSearchSubmit={onSearchSubmit}
    />
  );
}
