"use client";

import React, { useState, useEffect } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * A wrapper component that only renders its children on the client.
 * This helps prevent hydration mismatches by ensuring content with:
 * - Browser-specific APIs
 * - Time-dependent calculations
 * - Random values
 * - Locale formatting
 * Are only rendered client-side to prevent hydration errors
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // On first render, don't render the children to avoid hydration mismatch
  if (!isClient) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{children}</>;
}
