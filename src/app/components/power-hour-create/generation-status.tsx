import React, { useEffect, useState } from 'react';
import { PowerHourGenerationSteps } from '@/models/power-hour';
import { Progress, Spinner, Button } from '@nextui-org/react';
import { FaExclamationTriangle } from 'react-icons/fa';

interface GenerationStatusProps {
  currentStep?: PowerHourGenerationSteps;
  progress?: number;
  errorMessage?: string;
  onRetry?: () => void;
  loadingMessage?: string;
  hasVideos?: boolean;
  hasSongs?: boolean;
  videoCount?: number;
  songCount?: number;
}

const stepLabels: Record<string, string> = {
  [PowerHourGenerationSteps.None]: "Initializing...",
  [PowerHourGenerationSteps.GenerateSongList]: "Generating song list...",
  [PowerHourGenerationSteps.SongListToVideos]: "Finding videos for songs...",
  [PowerHourGenerationSteps.buildPowerHour]: "Building power hour...",
  [PowerHourGenerationSteps.Complete]: "Complete!",
  "ReplaceSongs": "Replacing songs..." // Handle server-side step not in client enum
};

// Step descriptions for more details
const stepDescriptions: Record<string, string> = {
  [PowerHourGenerationSteps.None]: "Getting ready to create your power hour...",
  [PowerHourGenerationSteps.GenerateSongList]: "Using AI to curate a perfect list of songs based on your theme",
  [PowerHourGenerationSteps.SongListToVideos]: "Searching for high-quality music videos for each song",
  [PowerHourGenerationSteps.buildPowerHour]: "Creating entries and finalizing your power hour",
  [PowerHourGenerationSteps.Complete]: "Your power hour is ready to explore!",
  "ReplaceSongs": "Swapping out songs to better match your request"
};

const GenerationStatus: React.FC<GenerationStatusProps> = ({
  currentStep,
  progress,
  errorMessage,
  onRetry,
  loadingMessage,
  hasVideos,
  hasSongs,
  videoCount,
  songCount
}) => {
  // Default to initial state if no data provided
  const step = currentStep || PowerHourGenerationSteps.None;
  const percentage = progress !== undefined ? progress : 0;
  const [animatedPercentage, setAnimatedPercentage] = useState(0);

  // Animate the progress bar
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, 300);
    return () => clearTimeout(timer);
  }, [percentage]);

  const statusText = loadingMessage || stepLabels[step] || "Processing...";
  
  // Enhanced status description that includes songs/videos count
  let statusDescription = stepDescriptions[step] || "";
  if (hasSongs && step === PowerHourGenerationSteps.SongListToVideos) {
    statusDescription = `Found ${songCount} songs for your theme. Now looking for the best videos.`;
  } else if (hasVideos && step === PowerHourGenerationSteps.buildPowerHour) {
    statusDescription = `Found ${videoCount} videos. Creating your power hour entries with the perfect clips.`;
  }

  // Use different colors based on progress or error state
  const progressColor = errorMessage ? "danger" : percentage >= 100 ? "success" : "primary";

  // If there's an error, show error state instead
  if (errorMessage) {
    return (
      <div className="w-full max-w-md mx-auto p-6 text-center bg-gray-800 rounded-lg shadow-lg transition-all duration-300">
        <div className="flex items-center justify-center mb-4">
          <FaExclamationTriangle className="text-danger mr-2 text-xl" />
          <h3 className="text-xl font-medium text-white">Generation Failed</h3>
        </div>

        <div className="bg-gray-900 p-4 rounded-md mb-4 overflow-auto max-h-40 text-left">
          <p className="text-danger mb-2">Error message:</p>
          <p className="text-sm text-gray-300 whitespace-pre-wrap">{errorMessage}</p>
        </div>

        <p className="text-md text-gray-300 mb-4">
          There was a problem creating your power hour. This could be due to API rate limits,
          connectivity issues, or problems with the selected songs.
        </p>

        {onRetry && (
          <Button
            color="primary"
            onClick={onRetry}
            className="mt-2"
          >
            Try Again
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto p-6 text-center bg-gray-800 rounded-lg shadow-lg transition-all duration-300">
      <div className="flex items-center justify-center mb-4">
        <Spinner size="sm" color={progressColor} className="mr-2" />
        <h3 className="text-xl font-medium text-white">{statusText}</h3>
      </div>

      <Progress
        size="md"
        radius="sm"
        value={animatedPercentage}
        color={progressColor}
        showValueLabel={true}
        className="max-w-md mb-4"
        classNames={{
          label: "text-white",
          value: "text-white"
        }}
      />

      <p className="text-md text-gray-300 mb-3">{statusDescription}</p>

      <div className="mt-3 mb-3">
        {hasSongs && (
          <div className="flex items-center text-sm text-green-400 mb-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Songs generated
          </div>
        )}
        {hasVideos && (
          <div className="flex items-center text-sm text-green-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Videos found
          </div>
        )}
      </div>

      <p className="text-sm text-gray-400">
        {percentage < 100
          ? "This may take a few minutes depending on the complexity of your request"
          : "Generation complete! Your power hour will display shortly."}
      </p>
    </div>
  );
};

export default GenerationStatus; 