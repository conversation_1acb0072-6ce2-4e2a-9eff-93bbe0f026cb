import { PowerHour, Song } from "@/models/power-hour";
import React, { useState, useEffect } from "react";

const SongsList = ({
	search,
	powerHour,
	setPowerHour,
}: {
	search: string;
	powerHour: PowerHour;
	setPowerHour: any;
}) => {
	const [songs, setSongs] = useState(powerHour?.songs ?? []);
	const [entries, setEntries] = useState(powerHour?.entries ?? []);
	const [isLoading, setIsLoading] = useState(true);
	const [isReplacing, setIsReplacing] = useState<boolean>(false);
	const [isValidatingVideos, setIsValidatingVideos] = useState<boolean>(false);

	useEffect(() => {
		if (powerHour.songs) {
			setSongs(
				powerHour.songs.map((s: Song) => ({
					...s,
					markedForReplacement: false,
				}))
			);
			setIsLoading(false);
		}
	}, [powerHour, powerHour.songs]);

	const toggleMarkForReplacement = (id: any) => {
		const updatedSongs = songs.map((song: Song) => {
			if (song.id === id) {
				return { ...song, markedForReplacement: !song.markedForReplacement };
			}
			return song;
		});
		setSongs(updatedSongs);
	};

	const replaceSongs = () => {
		setIsReplacing(true); // Start loading
		const badSongs = songs.filter((song: Song) => song.markedForReplacement);
		// .map((song: Song) => delete song.markedForReplacement);
		console.log(badSongs);
		// FirebaseFunctions.replaceSongs(search, badSongs).then((result) => {
		// 	if (!result?.songs) return console.error("failed to replace songs");
		// 	console.log("replacement songs", result.songs);
		// 	const updatedSongs = songs
		// 		.filter((song: Song) => !song.markedForReplacement)
		// 		.concat(
		// 			result?.songs?.map((s: Song) => ({ ...s, isReplacement: true }))
		// 		);
		// 	console.log(updatedSongs);
		// 	setPowerHour({
		// 		...powerHour,
		// 		songs: updatedSongs,
		// 	});
		// });
		setIsReplacing(false); // Stop loading
	};

	const submit = () => {
		// FirebaseFunctions.songListToVideos(songs)
		// 	.then((result) => {
		// 		setPowerHour({ ...powerHour, entries: result });
		// 		console.log(result);
		// 		return result.entries;
		// 	})
		// 	.then((entries) => {
		// 		const videoIds = entries?.map((entry: any) => entry.videoId);
		// 		validateVideos(videoIds);
		// 	});
	};

	const validateVideos = (videoIds: string[]) => {
		// if (!videoIds) return console.error("No entries returned");
		// FirebaseFunctions.validateVideos(videoIds).then((result) => {
		// 	console.log(result);
		// });
	};

	const anyMarkedForReplacement = songs.some(
		(song: Song) => song.markedForReplacement
	);

	return (
		<div className="flex h-full items-center justify-center bg-gray-800 text-white">
			{isLoading ? (
				<div className="flex justify-center items-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-300"></div>
				</div>
			) : songs.length > 0 ? (
				<div className="p-4 w-full">
					<div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-4">
						{songs.map((song) => (
							<div
								key={song.id}
								className="relative group cursor-pointer"
								onClick={() => toggleMarkForReplacement(song.id)}
							>
								<div className="p-4 bg-gray-900 rounded-lg">
									<p className="font-bold">{song.title}</p>
									<p>{song.artist}</p>
								</div>

								<div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
									<span className="text-white text-lg">
										{song.markedForReplacement ? "Keep" : "Replace"}
									</span>
								</div>
								{song.markedForReplacement && (
									<div className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-bl-lg">
										Replace
									</div>
								)}

								{song?.isReplacement && !song.markedForReplacement && (
									<div className="absolute top-0 right-0 p-1 bg-green-500 text-white rounded-bl-lg">
										New
									</div>
								)}
							</div>
						))}
					</div>
					<div className="mt-4 text-center">
						<button
							onClick={anyMarkedForReplacement ? replaceSongs : submit}
							disabled={isReplacing}
							className={`mx-auto px-6 py-2 rounded-md ${
								anyMarkedForReplacement
									? "bg-red-500 hover:bg-red-600"
									: "bg-green-500 hover:bg-green-600"
							} ${isReplacing ? "opacity-50 cursor-not-allowed" : ""}`} // Add opacity and cursor styles when isReplacing
						>
							{isReplacing ? (
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mx-auto"></div> // Spinner instead of text
							) : anyMarkedForReplacement ? (
								"Replace"
							) : (
								"Looks Good"
							)}
						</button>
					</div>
				</div>
			) : (
				<div>No songs available.</div>
			)}
		</div>
	);
};

export default SongsList;
