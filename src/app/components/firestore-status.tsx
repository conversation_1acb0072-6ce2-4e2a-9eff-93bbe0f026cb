"use client";

import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaExclamationTriangle, FaTimesCircle, FaSpinner } from 'react-icons/fa';

interface FirestoreStatusProps {
  error?: Error | null;
  connectionError?: Error | null;
  firestoreError?: Error | null;
  loading?: boolean;
  showDetails?: boolean;
}

const FirestoreStatus: React.FC<FirestoreStatusProps> = ({
  error,
  connectionError,
  firestoreError,
  loading = false,
  showDetails = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [connectionTest, setConnectionTest] = useState<'idle' | 'testing' | 'success' | 'failed'>('idle');

  // Determine the overall status
  const getStatus = () => {
    if (loading) return 'loading';
    if (connectionError) return 'connection-error';
    if (firestoreError) return 'firestore-error';
    if (error) return 'error';
    return 'connected';
  };

  const status = getStatus();

  // Get status display information
  const getStatusInfo = () => {
    switch (status) {
      case 'loading':
        return {
          icon: <FaSpinner className="animate-spin" />,
          color: 'text-blue-500',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Connecting...',
          message: 'Establishing connection to database'
        };
      case 'connected':
        return {
          icon: <FaCheckCircle />,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Connected',
          message: 'Database connection is healthy'
        };
      case 'connection-error':
        return {
          icon: <FaTimesCircle />,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Connection Failed',
          message: connectionError?.message || 'Unable to connect to database'
        };
      case 'firestore-error':
        return {
          icon: <FaExclamationTriangle />,
          color: 'text-orange-500',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          title: 'Database Error',
          message: firestoreError?.message || 'Database operation failed'
        };
      case 'error':
      default:
        return {
          icon: <FaTimesCircle />,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Error',
          message: error?.message || 'An unknown error occurred'
        };
    }
  };

  const statusInfo = getStatusInfo();

  // Test Firestore connection
  const testConnection = async () => {
    setConnectionTest('testing');
    try {
      const { firestore, COLLECTIONS } = await import("@/app/firebase/firebase");
      const { collection, getDocs, limit, query } = await import("firebase/firestore");
      
      if (!firestore) {
        throw new Error("Firestore not initialized");
      }
      
      // Try to fetch a small amount of data
      const testQuery = query(collection(firestore, COLLECTIONS.POWER_HOURS), limit(1));
      await getDocs(testQuery);
      
      setConnectionTest('success');
      setTimeout(() => setConnectionTest('idle'), 3000);
    } catch (error) {
      console.error("Connection test failed:", error);
      setConnectionTest('failed');
      setTimeout(() => setConnectionTest('idle'), 3000);
    }
  };

  if (!showDetails && status === 'connected') {
    return null; // Don't show anything if connected and details not requested
  }

  return (
    <div className={`rounded-lg border p-4 ${statusInfo.bgColor} ${statusInfo.borderColor}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={statusInfo.color}>
            {statusInfo.icon}
          </div>
          <div>
            <h3 className={`font-medium ${statusInfo.color}`}>
              {statusInfo.title}
            </h3>
            <p className="text-sm text-gray-600">
              {statusInfo.message}
            </p>
          </div>
        </div>
        
        {(error || connectionError || firestoreError) && (
          <div className="flex space-x-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? 'Hide' : 'Details'}
            </button>
            <button
              onClick={testConnection}
              disabled={connectionTest === 'testing'}
              className="text-sm bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded disabled:opacity-50"
            >
              {connectionTest === 'testing' && <FaSpinner className="animate-spin inline mr-1" />}
              {connectionTest === 'success' && <FaCheckCircle className="inline mr-1 text-green-400" />}
              {connectionTest === 'failed' && <FaTimesCircle className="inline mr-1 text-red-400" />}
              Test
            </button>
          </div>
        )}
      </div>

      {isExpanded && (error || connectionError || firestoreError) && (
        <div className="mt-4 space-y-2">
          {connectionError && (
            <div className="bg-white rounded p-3 border">
              <h4 className="font-medium text-red-700 mb-1">Connection Error</h4>
              <p className="text-sm text-red-600 font-mono">{connectionError.message}</p>
            </div>
          )}
          {firestoreError && (
            <div className="bg-white rounded p-3 border">
              <h4 className="font-medium text-orange-700 mb-1">Firestore Error</h4>
              <p className="text-sm text-orange-600 font-mono">{firestoreError.message}</p>
            </div>
          )}
          {error && !connectionError && !firestoreError && (
            <div className="bg-white rounded p-3 border">
              <h4 className="font-medium text-red-700 mb-1">General Error</h4>
              <p className="text-sm text-red-600 font-mono">{error.message}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FirestoreStatus;
