"use client";

import { PowerHour, PowerHourID } from "@/models/power-hour";
import React, { useEffect, useState } from "react";
import PowerHourCard from "./power-hour-card";
import { useRouter } from "next/navigation";
import { useFirestorePowerHours } from "@/hooks/use-firestore-power-hour";

const Landing = ({
  search,
  setSearch,
  count,
  setCount,
  onSearchSubmit,
}: {
  search: string;
  setSearch: any;
  count: number;
  setCount: any;
  onSearchSubmit?: (searchTerm: string) => void;
}) => {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState(search);

  // Use the Firestore hook to fetch all power hours directly
  // Note: We don't need a separate state because the hook already manages loading state
  const { powerHours, loading, error } = useFirestorePowerHours();

  // Log any errors from Firestore
  useEffect(() => {
    if (error) {
      console.error("Error fetching top power hours:", error);
    }
  }, [error]);

  const handleInputChange = (event: any) => {
    setSearchValue(event.target.value);
  };

  // Handler for the Generate button click
  const handleGenerateClick = () => {
    // If onSearchSubmit is provided, use it; otherwise just update the search state
    if (onSearchSubmit && typeof onSearchSubmit === "function") {
      onSearchSubmit(searchValue);
    } else {
      setSearch(searchValue);
    }
  };

  const handleKeyPress = (event: any) => {
    if (event.key === "Enter") {
      handleGenerateClick();
    }
  };

  const countSelectionChange = (keys: any) => {
    // Properly handle the NextUI selection object
    if (keys instanceof Set && keys.size > 0) {
      // Get the first key from the Set and parse it as a number
      const selectedKey = Array.from(keys)[0];
      if (typeof selectedKey === "string") {
        const numericValue = parseInt(selectedKey, 10);
        if (!isNaN(numericValue)) {
          setCount(numericValue);
        }
      }
    }
  };

  const goToActivePowerHour = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/active-power-hour?powerHourId=${powerHourId}`);
  };

  const goToDetails = (powerHourId: PowerHourID) => {
    router.push(`/power-hour-ai/power-hour-details?powerHourId=${powerHourId}`);
  };

  return (
    <div className="flex flex-col h-screen overflow-auto">
      <section className="min-h-screen w-full flex flex-col items-center justify-center bg-gray-800 text-white py-8">
        <div className="w-full max-w-6xl mx-auto flex flex-col items-center justify-center px-4">
          <div className="text-5xl font-bold mb-16">Power Hour AI</div>
          <div className="flex items-center w-full max-w-md">
            <input
              type="text"
              placeholder="Search..."
              className="form-input w-full px-4 py-2 border rounded-md shadow-sm text-gray-900 mr-4"
              value={searchValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
            />

            <button
              className={`px-6 py-2 rounded-md transition duration-150 ${
                searchValue
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-blue-300 text-gray-200"
              }`}
              onClick={handleGenerateClick}
              onSubmit={handleGenerateClick}
              disabled={!searchValue} // Disable button when searchValue is empty
            >
              Generate
            </button>
          </div>
          <div className="w-full max-w-md mb-10 mt-6">
            <div className="flex flex-col items-center">
              <div className="text-white mb-3 font-medium">
                Select number of videos:
              </div>
              <div className="flex flex-wrap justify-center gap-3">
                {[2, 5, 10, 30, 60].map((value) => (
                  <button
                    key={value}
                    onClick={() => setCount(value)}
                    className={`px-3 py-1 rounded-md transition duration-150 ${
                      count === value
                        ? "bg-blue-600 text-white"
                        : "bg-gray-700 text-white border border-gray-600 hover:bg-gray-600"
                    }`}
                  >
                    {value}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Example Cards Section */}
          <div className="flex flex-wrap justify-center items-stretch gap-6 w-full max-w-4xl mt-8">
            {/* Example Card 1 */}
            <div
              onClick={() =>
                setSearchValue("Top songs from each EDM sub genre")
              }
              className="flex flex-col items-center justify-between bg-gray-700 border border-gray-600 rounded-lg shadow-md p-4 w-full sm:w-1/3 cursor-pointer"
            >
              <h3 className="text-lg font-semibold mb-2">Be Creative</h3>
              <p>{'"Top songs from each EDM sub genre"'}</p>
            </div>

            {/* Example Card 2 */}
            <div
              onClick={() => setSearchValue("Only Taylor Swift")}
              className="flex flex-col items-center justify-start bg-gray-700 border border-gray-600 rounded-lg shadow-lg p-5 w-full sm:w-1/3 cursor-pointer hover:bg-gray-600 transition-colors duration-300"
            >
              <h3 className="text-lg font-semibold mb-2">By Artist</h3>
              <p>{'"Only Taylor Swift"'}</p>
            </div>

            {/* Example Card 3 */}
            <div
              onClick={() => setSearchValue("Grunge of the 90s")}
              className="flex flex-col items-center justify-between bg-gray-700 border border-gray-600 rounded-lg shadow-lg p-5 w-full sm:w-1/3 cursor-pointer hover:bg-gray-600 transition-colors duration-300"
            >
              <h3 className="text-lg font-semibold mb-2">From an Era</h3>
              <p>{'"Grunge of the 90s"'}</p>
            </div>
          </div>
        </div>
      </section>
      <section className="min-h-screen w-full flex flex-col items-center bg-gray-700 py-12">
        <div className="w-full max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-white text-center">
            All Power Hours
          </h2>
          <div className="w-full">
            {loading ? (
              <div className="flex items-center justify-center w-full h-40">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
              </div>
            ) : error ? (
              <div className="text-red-500 text-center w-full">
                Error loading power hours. Please try again later.
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {powerHours?.length > 0 ? (
                  powerHours.map((powerHour) => (
                    <PowerHourCard
                      key={powerHour.id}
                      powerHour={powerHour}
                      onClick={() => goToDetails(powerHour.id)}
                    />
                  ))
                ) : (
                  <div className="text-center col-span-full py-12 text-white text-xl">
                    No power hours available. Create one to get started!
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;
