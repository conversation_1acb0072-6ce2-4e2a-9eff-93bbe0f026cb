"use client";

import { initializeApp, getApps, FirebaseApp } from "firebase/app";
import { firebaseConfig } from "./firebase-config";
import { getFirestore, Firestore } from "firebase/firestore";

// Initialize Firebase only on the client side
let app: FirebaseApp | null = null;
let firestore: Firestore | null = null;
let initializationAttempts = 0;
let isInitializing = false;
let isInitialized = false;
let firestoreInitialized = false;
let initializationCallbacks: Array<() => void> = [];

/**
 * Check if Firestore is properly initialized and available
 */
export const isFirestoreAvailable = () => {
  return Boolean(app) && Boolean(firestore) && firestoreInitialized;
};

/**
 * Legacy function to maintain compatibility
 */
export const isFirebaseAvailable = () => {
  return isFirestoreAvailable();
};

/**
 * Initialize Firebase with retries and notify when done
 */
const initializeFirebase = () => {
  // Only initialize on client side
  if (typeof window === 'undefined') {
    console.log('Not initializing Firebase - running on server');
    return;
  }
  
  // Prevent multiple simultaneous initialization attempts
  if (isInitializing) {
    console.log('Firebase initialization already in progress');
    return;
  }
  
  // If already initialized successfully, just run callbacks
  if (isInitialized && isFirebaseAvailable()) {
    console.log('Firebase already initialized');
    runInitCallbacks();
    return;
  }
  
  isInitializing = true;
  initializationAttempts++;
  
  console.log(`Initializing Firebase (attempt ${initializationAttempts})...`);
  
  try {
    // Check if Firebase has already been initialized
    if (!getApps().length) {
      console.log('Creating new Firebase app instance');
      app = initializeApp(firebaseConfig);
    } else {
      console.log('Using existing Firebase app instance');
      app = getApps()[0];
    }
    
    if (!app) {
      throw new Error('Failed to get Firebase app instance');
    }
    
    // Initialize Firestore (primary database used by n8n)
    console.log('Initializing Firebase Firestore');
    try {
      firestore = getFirestore(app);
      if (!firestore) {
        throw new Error('Failed to initialize Firestore');
      }
      firestoreInitialized = true;
      console.log('Firebase Firestore initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Firestore:', error);
    }
    
    // RTDB removed - using only Firestore
    console.log('Skipping RTDB initialization - using only Firestore');
    
    // Mark initialization as successful if at least Firestore is available
    isInitialized = firestoreInitialized;
    isInitializing = false;
    
    if (isInitialized) {
      console.log('Firebase initialization completed successfully with Firestore available');
      
      // Run any callbacks waiting for initialization
      runInitCallbacks();
    } else {
      console.error('Firebase initialization failed - Firestore is required');
    }
    
  } catch (error) {
    console.error('Firebase initialization failed:', error);
    isInitializing = false;
    
    // Reset if initialization failed
    if (!isInitialized) {
      app = null;
      firestore = null;
    }
    
    // Retry initialization with exponential backoff (max 5 attempts)
    if (initializationAttempts < 5) {
      const delay = Math.pow(2, initializationAttempts) * 100;
      console.log(`Retrying Firebase initialization in ${delay}ms...`);
      setTimeout(initializeFirebase, delay);
    } else {
      console.error('Maximum Firebase initialization attempts reached');
    }
  }
};

/**
 * Run all registered initialization callbacks
 */
const runInitCallbacks = () => {
  console.log(`Running ${initializationCallbacks.length} Firebase initialization callbacks`);
  initializationCallbacks.forEach(callback => {
    try {
      callback();
    } catch (error) {
      console.error('Error in Firebase initialization callback:', error);
    }
  });
  initializationCallbacks = [];
};

/**
 * Register a callback to be executed when Firebase is initialized
 * @param callback Function to call when Firebase is ready
 */
export const onFirebaseReady = (callback: () => void) => {
  if (isInitialized && isFirebaseAvailable()) {
    // Firebase is already ready, call immediately
    callback();
  } else {
    // Queue the callback for when Firebase is ready
    initializationCallbacks.push(callback);
    
    // Start initialization if not already started
    if (!isInitializing && typeof window !== 'undefined') {
      initializeFirebase();
    }
  }
};

// Initialize Firebase if in browser environment
if (typeof window !== 'undefined') {
  // Wait for the DOM to be ready
  if (document.readyState === 'complete') {
    // DOM is already ready, initialize now
    initializeFirebase();
  } else {
    // Wait for the DOM to be ready
    window.addEventListener('load', () => {
      initializeFirebase();
    });
  }
}

// Export Firestore only
export { firestore };

// Export convenient constants for collection paths
export const COLLECTIONS = {
  POWER_HOURS: 'power-hours'
};









