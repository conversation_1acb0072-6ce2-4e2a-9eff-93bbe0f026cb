"use client";

import React from "react";
import PowerHourAI from "../components/power-hour-ai";
import { ToastContainer } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import ClientOnly from "../components/utils/client-only";

export default function Page() {
	return (
		<ClientOnly fallback={<div className="h-full w-full flex items-center justify-center">Loading...</div>}>
			<div className="h-full w-full"> 
				<PowerHourAI />
			</div>
		</ClientOnly>
	);
}
