"use client";

import React from "react";

interface PowerHourAILayoutProps {
  children: React.ReactNode;
}

/**
 * Layout component for Power Hour Active page
 * Provides a full-height container for the power hour player
 */
export default function PowerHourAILayout({ children }: PowerHourAILayoutProps) {
  return (
    <section 
      className="power-hour-layout" 
      style={{ height: 'calc(100vh - 64px)' }}
    >
      {children}
    </section>
  );
}
