"use client";

import ActivePowerHour from "@/app/components/power-hour-active/active-power-hour";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import { <PERSON><PERSON> } from "@nextui-org/react";

// Declare JSX namespace to fix the 'JSX element implicitly has type any' errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}

/**
 * PowerHourPage component that handles the display of an active power hour
 * Responsibilities:
 * 1. Validate URL parameters (powerHourId is required)
 * 2. <PERSON>le errors gracefully with user feedback
 * 3. Render the ActivePowerHour component when parameters are valid
 */
export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [validationState, setValidationState] = useState<{
    isValid: boolean;
    isValidating: boolean;
    powerHourId: string | null;
    errorMessage: string | null;
  }>({
    isValid: false,
    isValidating: true,
    powerHourId: null,
    errorMessage: null
  });
  
  // Extract and validate URL parameters
  useEffect(() => {
    const powerHourId = searchParams.get("powerHourId");
    
    if (!powerHourId) {
      setValidationState({
        isValid: false,
        isValidating: false,
        powerHourId: null,
        errorMessage: "No Power Hour ID provided."
      });
      
      // Log error for debugging
      console.error("Missing required powerHourId URL parameter");
      
      // Show user-friendly error
      toast.error("No Power Hour ID provided. Redirecting to home page.");
      
      // Redirect to home page after a short delay
      setTimeout(() => {
        router.push('/');
      }, 2000);
    } else {
      // Valid parameters
      setValidationState({
        isValid: true,
        isValidating: false,
        powerHourId,
        errorMessage: null
      });
    }
  }, [searchParams, router]);

  const handleGoHome = () => {
    router.push('/');
  };

  // Render loading state, error state or the actual power hour component
  return (
    <div className="container mx-auto px-4 py-8">
      {validationState.isValidating ? (
        // Loading state while validating parameters
        <div className="text-center p-8 bg-gray-800 rounded-lg shadow-lg animate-pulse">
          <h2 className="text-2xl font-bold text-white mb-4">Validating Power Hour...</h2>
        </div>
      ) : !validationState.isValid ? (
        // Error state with user-friendly message and action
        <div className="text-center p-8 bg-gray-800 rounded-lg shadow-lg border border-red-500">
          <h2 className="text-2xl font-bold text-white mb-4">Unable to Load Power Hour</h2>
          <p className="text-gray-300 mb-6">{validationState.errorMessage || "An unknown error occurred."}</p>
          <Button 
            color="primary" 
            onClick={handleGoHome}
            className="mx-auto"
          >
            Return to Home
          </Button>
        </div>
      ) : (
        // Successfully validated - pass the power hour ID to the component
        <ActivePowerHour />
      )}
    </div>
  );
}
