"use client";

import React from "react";
import { CircularProgress } from "@nextui-org/react";

// Declare JSX namespace to fix any TypeScript errors
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}

/**
 * Loading component displayed while the Power Hour page is loading
 */
export default function Loading() {
  return (
    <div className="h-full w-full flex items-center justify-center bg-gray-800">
      <CircularProgress size="lg" aria-label="Loading power hour..." />
    </div>
  );
}
