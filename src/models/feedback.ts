import { PowerHourID } from "./power-hour";

export interface PHFeedback {
  powerHourId: PowerHourID;
  upvotes: number;
  downvotes: number;
  feedback: { [key in PowerHourFeedbackOptions]: number };
}

export interface PHEntryFeedback {
  powerHourId: PowerHourID;
  powerHourEntryId: string;
  upvotes: number;
  downvotes: number;
  feedback?: Partial<{ [key in PHEFeedbackOpts]: number }>;
  timestamp: number;
}



export enum PowerHourFeedbackOptions {
  ProblemWithVideos = "Problem with videos",
  ProblemWithSongs = "Problem with songs",
  ProblemWithPlayback = "Problem with YouTube",
  ProblemWithPowerHour = "Problem with Power Hour"
}


export enum PHEFeedbackOpts {
  ProblemWithVideo = "Problem with video",
  ProblemWithSong = "Problem with song",
  ProblemWithPlayback = "Problem with YouTube",
  SongDoesntMatchVideo = "Song doesn't match video",
  VideoIsntMusicVideo = "Video isn't music video",
  SongIsntMusic = "Song isn't music",
  SongDoesntMatchTitle = "Song doesn't match title",
}