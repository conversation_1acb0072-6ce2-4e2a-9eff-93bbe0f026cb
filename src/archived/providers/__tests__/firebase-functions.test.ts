import { toast } from 'react-toastify';
import { FirebaseFunctions } from '../firebase-functions';
import { PHActions } from '../../../../functions/src/[models]/enums';
import { PHOperations } from '@/models/enums';

// Mock fetch globally
global.fetch = jest.fn();

// Mock the toast notifications
jest.mock('react-toastify');

describe('Firebase Functions Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the fetch mock
    (global.fetch as jest.Mock).mockReset();
  });

  describe('pipeline', () => {
    it('should execute a pipeline successfully', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          powerHourId: 'test-power-hour-id',
          songs: [{ id: 'song1', title: 'Test Song' }],
          entries: [{ id: 'entry1', songId: 'song1', videoId: 'video1' }]
        }),
      });

      const payload = {
        search: 'Test Search Term',
        powerHourId: 'test-power-hour-id',
        count: 10
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.GenerateSongsAI]
      );

      // Verify fetch was called with correct params
      expect(global.fetch).toHaveBeenCalled();
      
      // Verify body contains expected properties
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);
      expect(requestBody).toHaveProperty('search', 'Test Search Term');
      
      // Verify result structure
      expect(result).toHaveProperty('powerHourId', 'test-power-hour-id');
      expect(result).toHaveProperty('songs');
      expect(result).toHaveProperty('entries');
    });

    it('should handle API error response', async () => {
      // Mock an error response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({
          error: 'Function execution error',
          message: 'Failed to generate power hour',
          timestamp: '2025-05-14T10:56:38-07:00',
        }),
      });

      const payload = {
        search: 'Test Search Term',
        count: 10
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.GenerateSongsAI]
      );

      // Verify error toast notification
      expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('Function execution error'));

      // Verify error result structure
      expect(result).toHaveProperty('error');
      expect(result.error).toHaveProperty('error', 'Function execution error');
    });

    it('should handle network errors', async () => {
      // Mock a network error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network failure'));

      const payload = {
        search: 'Test Search Term',
        count: 10
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.GenerateSongsAI]
      );

      // Verify error toast notification
      expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('Network failure'));

      // Verify error result structure
      expect(result).toHaveProperty('error');
      expect(result.error).toHaveProperty('error', 'Network failure');
      expect(result.error).toHaveProperty('errorCode', 'NETWORK_ERROR');
    });
  });

  describe('different pipeline operations', () => {
    it('should execute replace songs operation', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          powerHourId: 'test-power-hour-id',
          songs: [{ id: 'new-song', title: 'New Test Song' }],
        }),
      });

      const payload = {
        powerHourId: 'test-power-hour-id',
        songs: [{ id: 'song1', title: 'Old Song', markedForReplacement: true }]
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.ReplaceSongsAI]
      );

      // Verify fetch was called with correct params
      expect(global.fetch).toHaveBeenCalled();
      
      // Verify result structure
      expect(result).toHaveProperty('powerHourId', 'test-power-hour-id');
      expect(result).toHaveProperty('songs');
    });

    it('should execute locate videos operation', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          powerHourId: 'test-power-hour-id',
          videos: [{ id: 'video1', title: 'Test Video', url: 'https://example.com/video' }],
        }),
      });

      const payload = {
        powerHourId: 'test-power-hour-id',
        songs: [{ id: 'song1', title: 'Test Song' }]
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.LocateVideosYT]
      );

      // Verify fetch was called with correct params
      expect(global.fetch).toHaveBeenCalled();
      
      // Verify result structure
      expect(result).toHaveProperty('powerHourId', 'test-power-hour-id');
      expect(result).toHaveProperty('videos');
    });

    it('should execute build power hour entries operation', async () => {
      // Mock a successful response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          powerHourId: 'test-power-hour-id',
          entries: [{ 
            id: 'entry1', 
            songId: 'song1', 
            videoId: 'video1',
            song: { id: 'song1', title: 'Test Song' },
            video: { id: 'video1', title: 'Test Video' }
          }],
        }),
      });

      const payload = {
        powerHourId: 'test-power-hour-id',
        songs: [{ id: 'song1', title: 'Test Song' }],
        videos: [{ id: 'video1', title: 'Test Video', url: 'https://example.com/video' }]
      };
      
      const result = await FirebaseFunctions.pipeline(
        payload,
        PHActions.GeneratePowerHour,
        [PHOperations.buildPHEntries]
      );

      // Verify fetch was called with correct params
      expect(global.fetch).toHaveBeenCalled();
      
      // Verify result structure
      expect(result).toHaveProperty('powerHourId', 'test-power-hour-id');
      expect(result).toHaveProperty('entries');
    });
  });
});
