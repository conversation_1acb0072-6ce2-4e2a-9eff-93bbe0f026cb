/*
 * ARCHIVED CODE - DO NOT USE
 * This file was archived on May 14, 2025
 * Reason: Redundant implementation replaced by the more comprehensive casting-manager.tsx
 * All components using this provider should be updated to use the useCasting hook from casting-manager.tsx instead
 */

"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useActiveVideo } from "./active-video-provider";

type CastState = {
  isAvailable: boolean;
  isCasting: boolean;
  castSession: any;
  castError: string | null;
};

interface CastContextType extends CastState {
  initializeCast: () => void;
  startCasting: (videoId: string, startTime?: number, endTime?: number) => Promise<void>;
  stopCasting: () => void;
  castAvailable: boolean;
}

const defaultCastContext: CastContextType = {
  isAvailable: false,
  isCasting: false,
  castSession: null,
  castError: null,
  castAvailable: false,
  initializeCast: () => {},
  startCasting: async () => {},
  stopCasting: () => {},
};

const CastContext = createContext<CastContextType>(defaultCastContext);

export const useCast = () => useContext(CastContext);

export function CastProvider({ children }: { children: React.ReactNode }) {
  const [castState, setCastState] = useState<CastState>({
    isAvailable: false,
    isCasting: false,
    castSession: null,
    castError: null,
  });
  const [castAvailable, setCastAvailable] = useState(false);
  const { playerRef, currentTime } = useActiveVideo();

  // Initialize the Cast API
  const initializeCast = () => {
    if (typeof window === 'undefined' || !(window as any).chrome) {
      console.log("Chrome Cast API not available");
      return;
    }

    // Initialize the Cast API
    const initializeCastApi = () => {
      const context = (window as any).cast.framework.CastContext.getInstance();
      context.setOptions({
        receiverApplicationId: (window as any).chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,
        autoJoinPolicy: (window as any).chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED,
      });

      // Listen for cast availability
      context.addEventListener(
        (window as any).cast.framework.CastContextEventType.CAST_STATE_CHANGED,
        (event: any) => {
          const castState = context.getCastState();
          setCastAvailable(
            castState === (window as any).cast.framework.CastState.CONNECTED || 
            castState === (window as any).cast.framework.CastState.CONNECTING ||
            castState === (window as any).cast.framework.CastState.NOT_CONNECTED
          );
          
          setCastState(prevState => ({
            ...prevState,
            isAvailable: true,
            isCasting: castState === (window as any).cast.framework.CastState.CONNECTED,
            castSession: castState === (window as any).cast.framework.CastState.CONNECTED ? 
              context.getCurrentSession() : null
          }));
        }
      );
    };

    // Wait for the Cast API to be available
    if ((window as any).cast && (window as any).cast.framework) {
      initializeCastApi();
    } else {
      (window as any)['__onGCastApiAvailable'] = (isAvailable: boolean) => {
        if (isAvailable) {
          initializeCastApi();
        }
      };
    }
  };

  // Start casting a YouTube video
  const startCasting = async (videoId: string, startTime = 0, endTime = 60) => {
    try {
      if (!castState.isAvailable) {
        throw new Error("Cast is not available");
      }

      const context = (window as any).cast.framework.CastContext.getInstance();
      let session = context.getCurrentSession();

      if (!session) {
        // Request a session
        const sessionRequest = new (window as any).cast.framework.SessionRequest(
          (window as any).chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID
        );
        const castSession = await context.requestSession(sessionRequest);
        session = castSession;
      }

      // Prepare YouTube media info
      const mediaInfo = new (window as any).chrome.cast.media.MediaInfo(
        `https://www.youtube.com/watch?v=${videoId}`,
        'video/mp4'
      );
      
      // Add customData for YouTube
      const customData = {
        type: 'youtube',
        videoId: videoId,
        startTime: startTime,
        endTime: endTime
      };
      
      mediaInfo.customData = customData;
      mediaInfo.contentId = videoId;
      
      // Create metadata for better display
      const metadata = new (window as any).chrome.cast.media.GenericMediaMetadata();
      metadata.title = `YouTube Video (ID: ${videoId})`;
      metadata.subtitle = 'Power Hour AI';
      mediaInfo.metadata = metadata;
      
      // Set up cast options
      const loadRequest = new (window as any).chrome.cast.media.LoadRequest(mediaInfo);
      loadRequest.currentTime = startTime;
      
      // Load the media
      const player = new (window as any).cast.framework.RemotePlayer();
      const controller = new (window as any).cast.framework.RemotePlayerController(player);
      
      await session.loadMedia(loadRequest);
      
      setCastState({
        ...castState,
        isCasting: true,
        castSession: session,
        castError: null,
      });
    } catch (error: any) {
      console.error("Casting error:", error);
      setCastState({
        ...castState,
        castError: error.message || "Unknown casting error",
      });
    }
  };

  // Stop casting
  const stopCasting = () => {
    if (castState.castSession) {
      castState.castSession.endSession(true);
      setCastState({
        ...castState,
        isCasting: false,
        castSession: null,
      });
    }
  };

  // Initialize the Cast API on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      initializeCast();
    }
    
    return () => {
      // Clean up
      if (castState.isCasting) {
        stopCasting();
      }
    };
  }, []);

  // Sync video position with cast when casting
  useEffect(() => {
    if (castState.isCasting && castState.castSession && currentTime) {
      try {
        const media = castState.castSession.getMediaSession();
        if (media) {
          // Only seek if the difference is more than 5 seconds to avoid constant seeking
          const currentCastTime = media.getEstimatedTime();
          if (Math.abs(currentTime - currentCastTime) > 5) {
            const request = new (window as any).chrome.cast.media.SeekRequest();
            request.currentTime = currentTime;
            media.seek(request);
          }
        }
      } catch (error) {
        console.error("Error syncing cast position:", error);
      }
    }
  }, [currentTime, castState.isCasting, castState.castSession]);

  return (
    <CastContext.Provider
      value={{
        ...castState,
        castAvailable,
        initializeCast,
        startCasting,
        stopCasting,
      }}
    >
      {children}
    </CastContext.Provider>
  );
}
