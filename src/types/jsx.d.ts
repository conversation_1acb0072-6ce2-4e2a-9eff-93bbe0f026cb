// Custom TypeScript declarations for JSX elements
import React from 'react';

declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

// Additional declarations for react-toastify
declare module "react-toastify" {
  export const toast: {
    success: (message: string) => void;
    error: (message: string) => void;
    info: (message: string) => void;
    warning: (message: string) => void;
  };
  export const ToastContainer: React.FC<any>;
}
